/* eslint-disable */
<template>
    <ConfirmEmail :isVisible="showConfirm" :toggleConfirmEmail="toggleConfirmEmail" />
    <div class="absolute top-0 left-0 w-full h-full bg-white z-50">
        <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgColor" />
        <div class="flex h-screen w-full">
            <!-- Left Form Section -->
            <div class="w-full lg:w-1/2 p-8 lg:p-12 overflow-y-auto bg-white">
                <div class="max-w-[350px] lg:max-w-[400px] mx-auto">
                    <!-- Logo & Title -->
                    <div class="w-full flex flex-col items-center gap-4 mb-8">
                        <router-link to="/" class="hover:opacity-90 transition-opacity duration-200">
                            <img loading="lazy" decoding="async" src="@/assets/Images/go_logo.svg" alt="Recruitable" class="max-w-[180px] mb-8 mx-auto" />
                        </router-link>
                        <h2 class="text-lg md:text-2xl font-bold text-gray-800 text-center">{{ $t("Create Your FreeAccount") }}</h2>
                        <p class="text-gray-500 text-base">{{ $t("Start your recruitment journey with us") }}</p>
                    </div>

                    <!-- Combined Email & Password Step -->
                    <div v-show="currentStep === 1" class="space-y-6">
                        <div class="space-y-4">
                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <input
                                        type="email"
                                        v-model="email"
                                        placeholder="Work email"
                                        class="w-full px-4 py-3 pl-10 border border-[#e5e5ef] rounded-xl shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-2"
                                        :class="{ 'border-red-500': errors.email }"
                                    />
                                </div>
                                <p v-if="errors.email" class="text-red-500 text-sm mt-1 text-left">{{ errors.email }}</p>
                            </div>

                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                        />
                                    </svg>
                                    <input
                                        :type="showPassword ? 'text' : 'password'"
                                        v-model="password"
                                        placeholder="Create password"
                                        class="w-full px-4 py-3 pl-10 pr-4 border border-[#e5e5ef] rounded-xl shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-2"
                                        :class="{ 'border-red-500': errors.password }"
                                    />
                                </div>
                                <p v-if="errors.password" class="text-red-500 text-sm mt-1 text-left">{{ errors.password }}</p>
                            </div>

                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                        />
                                    </svg>
                                    <input
                                        :type="showConfirmPassword ? 'text' : 'password'"
                                        v-model="confirmPassword"
                                        placeholder="Confirm password"
                                        class="w-full px-4 py-3 pl-10 pr-4 border border-[#e5e5ef] rounded-xl shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-2"
                                        :class="{ 'border-red-500': errors.confirmPassword }"
                                    />
                                </div>
                                <p v-if="errors.confirmPassword" class="text-red-500 text-sm mt-1 text-left">{{ errors.confirmPassword }}</p>
                            </div>
                        </div>

                        <button @click="submitAccount" class="w-full bg-NeonBlue text-white py-3 rounded-xl font-semibold hover:opacity-85">Create Account</button>
                    </div>

                    <div class="mt-8 text-gray-600">
                        {{ $t("Already have an account?") }}
                        <router-link to="/login" class="text-NeonBlue font-semibold hover:opacity-85 hover:underline">
                            {{ $t("Sign in") }}
                        </router-link>
                    </div>
                </div>
            </div>

            <!-- Right Graphic Section -->
            <div class="hidden lg:block w-1/2 h-screen bg-NeonBlue overflow-hidden">
                <div class="h-full flex flex-col items-center justify-center p-12 text-white">
                    <h2 class="text-4xl font-bold mb-6 text-center leading-tight">{{ $t("Find Top Talent Faster") }}</h2>
                    <p class="text-lg text-center mb-8 opacity-90">{{ $t("Join companies worldwide hiring smarter with our AI - powered recruitment platform") }}</p>

                    <img src="@/assets/Images/Automated-Workflow.png" alt="Automated Workflow" class="w-full max-w-xs mx-auto" />
                </div>
            </div>
        </div>

        <!-- Details Popup -->
        <div v-if="showDetailsPopup" class="fixed inset-0 backdrop-blur-xl bg-white/95 flex items-center justify-center p-4 z-50">
            <div class="bg-white rounded-xl p-12 max-w-6xl w-full min-h-[450px] border shadow-2xl flex flex-col">
                <!-- Popup Header -->
                <div class="mb-8 text-left">
                    <h2 class="text-2xl font-bold text-gray-900">{{ $t(" My Profile ") }}</h2>
                    <h3 class="text-lg font-semibold text-gray-800 mt-4">{{ $t("Personal Information") }}</h3>
                    <p class="text-gray-500 text-sm mt-1">{{ $t("Update your personal details") }}</p>
                </div>
                <!-- Form Content -->
                <div class="flex-1 flex flex-col justify-center">
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <input
                                    type="text"
                                    v-model="firstName"
                                    placeholder="First name"
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    :class="{ 'border-red-500': errors.firstName }"
                                />
                                <p v-if="errors.firstName" class="text-red-500 text-sm mt-1 text-left">{{ errors.firstName }}</p>
                            </div>
                            <div>
                                <input
                                    type="text"
                                    v-model="lastName"
                                    placeholder="Last name"
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    :class="{ 'border-red-500': errors.lastName }"
                                />
                                <p v-if="errors.lastName" class="text-red-500 text-sm mt-1 text-left">{{ errors.lastName }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <input
                                    type="text"
                                    v-model="companyName"
                                    placeholder="Company name"
                                    class="w-full px-4 py-3 rounded-xl border border-gray-300 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    :class="{ 'border-red-500': errors.companyName }"
                                />
                                <p v-if="errors.companyName" class="text-red-500 text-sm mt-1 text-left">{{ errors.companyName }}</p>
                            </div>
                            <div>
                                <div class="relative">
                                    <select
                                        v-model="companySize"
                                        class="w-full px-4 py-3 rounded-xl border border-gray-300 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white appearance-none"
                                        :class="{ 'border-red-500': errors.companySize }"
                                    >
                                        <option value="" disabled selected>Company size</option>
                                        <option v-for="(size, index) in companySizes" :key="index" :value="size">
                                            {{ size }}
                                        </option>
                                    </select>
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -***********" width="24px" fill="#1f1f1f"><path d="M480-360 280-560h400L480-360Z" /></svg>
                                    </div>
                                </div>
                                <p v-if="errors.companySize" class="text-red-500 text-sm mt-1 text-left">
                                    {{ errors.companySize }}
                                </p>
                            </div>
                        </div>

                        <div class="mt-auto pt-6 flex justify-end">
                            <button @click="submitProfile" class="w-1/6 h-11 bg-NeonBlue text-white rounded-xl font-semibold hover:opacity-85">
                                <span>Complete</span>
                                <LoaderComponent v-if="loading" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useStore } from "../../../store/index";
import ConfirmEmail from "./ConfirmEmail.vue";
import ToastNotification from "./ToastNotification.vue";
import { BASE_URL } from "@/constants";
import VueCookies from "vue-cookies";
import axios from "axios";
import { useRoute } from "vue-router";
export default {
    components: {
        ConfirmEmail,
        ToastNotification,
    },
    data() {
        return {
            loading: false,
            currentStep: 2,
            showDetailsPopup: true,
            /*
            recruiter: {
                first_name: "",
                last_name: "",
                email: "",
                password: "",
                confirm_password: "",
                company_name: [],
            },
            company: {
                name: "",
                employees_count: "",
            },
            */
            email: "",
            password: "",
            confirmPassword: "",
            firstName: "",
            lastName: "",
            bgColor: "red",
            token: "",
            companyName: "",
            companySize: "",
            emailVerify: "",
            userID: "",
            companyID: "",
            completeRegister: false,

            companySizes: ["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"],
            showPassword: false,
            showConfirmPassword: false,
            showConfirm: false,
            errors: {},
            message: "",
            isVisible: false,
            verificationToken: null,
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    mounted() {
        // Check for verification token in URL
        const urlParams = new URLSearchParams(window.location.search);
        this.verificationToken = urlParams.get("token");
        const route = useRoute();
        this.emailVerify = route.query.email;
        this.userID = route.query.id;
        this.companyID = route.query.company;
        this.token = route.query.token;

        if (route.query.email & route.query.id) this.completeRegister = true;
    },
    methods: {
        async submitAccount() {
            this.errors = {};
            if (!this.email) this.errors.email = "Email is required";
            if (!this.password) this.errors.password = "Password is required";
            if (!this.confirmPassword) this.errors.confirmPassword = "Confirm password is required";
            if (this.password !== this.confirmPassword) this.errors.confirmPassword = "Passwords do not match";

            if (Object.keys(this.errors).length > 0) return;

            try {
                await axios
                    .post(
                        `${BASE_URL}/company/registerAccount`,
                        {
                            email: this.email,
                            password: this.password,
                        },
                        { withCredentials: true },
                    )
                    .then((response) => {
                        // Show email confirmation popup

                        console.log("response");
                        console.log(response);
                        console.log("response");
                        this.showConfirm = true;
                        this.showDetailsPopup = true;
                    });
                // show profile completion popup after successful verification
            } catch (error) {
                this.message = error.response?.data?.message || "Registration failed";
                this.isVisible = true;
                setTimeout(() => (this.isVisible = false), 3000);
            }
        },

        async submitProfile() {
            this.errors = {};
            if (!this.firstName) this.errors.firstName = "First name is required";
            if (!this.lastName) this.errors.lastName = "Last name is required";
            if (!this.companyName) this.errors.companyName = "Company name is required";
            if (!this.companySize) this.errors.companySize = "Please select company size";

            if (Object.keys(this.errors).length > 0) return;

            try {
                if (this.emailVerify) {
                    await axios
                        .post(
                            `${BASE_URL}/company/registerCompany`,
                            {
                                email: this.emailVerify,
                                userID: this.userID,
                                firstName: this.firstName,
                                lastName: this.lastName,
                                token: this.token,
                                companyID: this.companyID,
                                companyName: this.companyName,
                                companySize: this.companySize,
                            },
                            { withCredentials: true },
                        )
                        .then((response) => {
                            if (response.status === 200) {
                                this.message = "Profile completion with success";
                                this.bgColor = "success";
                                // src\components\ToastNotification.vue 'success' is green otherwise red
                                this.isVisible = true;

                                setTimeout(() => ((this.isVisible = false), (this.loading = false)), 2000);

                                this.Store.isLoading = true;
                                VueCookies.set("userLogged", true, "24h");
                                this.$router.push(`/home?company_name=${response.data.company_name}`);
                                // this.userLoggedIn();
                                this.Store.userLogged();
                                this.Store.getCompanyCredit();
                            }
                        })
                        .then(() => {
                            axios
                                .get(`${BASE_URL}/user/isLoggedIn`, {
                                    withCredentials: true,
                                })
                                .then(() => {
                                    this.$gtag.event("login", { method: "Google" });
                                })
                                .catch((error) => {
                                    console.error("Error:", error);
                                });
                        });
                }
            } catch (error) {
                this.message = error.response?.data?.message || "Profile completion failed";
                this.bgColor = "fail";
                this.isVisible = true;
                setTimeout(() => (this.isVisible = false), 3000);
            }
        },

        toggleConfirmEmail() {
            this.showConfirm = !this.showConfirm;
        },
    },
};
</script>
