const mongoose = require("mongoose");
const autopopulate = require('mongoose-autopopulate');

const capturedPhotosSchema = mongoose.Schema({
    idCandidate: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Cheater',
        required: true,
        autopopulate: true,
    },
    image: {
        type: String
    },
    Date: {
        type: Date,
        default: Date.now,
        //  required: true,
    },
});
capturedPhotosSchema.plugin(autopopulate);
const CapturedPhotos = mongoose.model('CapturedPhotos', capturedPhotosSchema);
module.exports = CapturedPhotos