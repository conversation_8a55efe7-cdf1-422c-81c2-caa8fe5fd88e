<template>
  <div class="window">
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />

    <main class="container">
      <Transition v-if="show && !this.assessementStore.isTalent" name="slide-fade">
        <LoaderComponent v-if="animation" />

        <WelcomeCard v-if="passed == 1" class="first_page" :key="this.jobPosition" :key1="this.key1" :key2="this.key2"
          :key3="this.key3" :key4="this.key4" :key5="this.key5" :company="this.company" :jobPosition="this.jobPosition"
          @start="start" />
        <WelcomeCardpasstest v-else class="first_page" :key="this.company" :key1="this.key1" :key2="this.key2"
          :key3="this.key3" :key4="this.key4" :key6="this.key6" :company="this.company"
          :jobPosition="this.jobPosition" />
      </Transition>
      <Transition v-else name="slide-fade">
        <TestBox v-if="step == 1" button-text="Next" :handleClick="next" :isBtnLoading="isLoading"
          inside-div-class="second_page flex flex-col justify-center items-center " added-class="w-11/12">
          <form action="POST" @submit.prevent ref="form">
            <div class="input_group2">
              <div class="input_group">
                <label for="first_name" class="required">First Name</label>
                <input required type="text" id="first_name" placeholder="Enter your first name" v-model="first_name" name="first_name" />
                <span v-if="requiredField.first_name" class="err_msg">{{
                  requiredField.first_name
                }}</span>
              </div>
              <div class="input_group">
                <label for="last_name" class="required">Last Name</label>
                <input required type="text" id="last_name" placeholder="Enter your last name" v-model="last_name" name="last_name" />
                <span v-if="requiredField.last_name" class="err_msg">{{
                  requiredField.last_name
                }}</span>
              </div>
            </div>
            <div class="input_group2">
              <div class="input_group">
                <label for="email" class="required">Email</label>
                <input type="email" id="email" placeholder="Enter your email" required :value="key1" name="email" readonly />
              </div>
              <div class="input_group">
                <label for="phone" class="required">Phone Number</label>
                <div class="phone-input-container">
                  <VueCountryCode @onSelect="onSelect" :preferredCountries="['dz', 'fr', 'us', 'gb']" />
                  <input
                    required
                    type="tel"
                    id="phone"
                    v-model="phone"
                    name="phone"
                    minlength="10"
                    maxlength="18"
                    pattern="^[+][0-9]{11,17}$"
                    title="Please enter a valid phone number"
                    placeholder="Enter phone number"
                    oninput="this.setCustomValidity(''); this.value = this.value.replace(/[^\d+]/g, '').replace(/(?!^)[+]/g, '')"
                    oninvalid="this.setCustomValidity('Please enter a valid phone number with country code')"
                  />
                </div>
                <span v-if="requiredField.phone" class="err_msg">{{
                  requiredField.phone
                }}</span>
              </div>
            </div>

            <div class="media-chooser">
              <!-- Mode Selection -->
              <div class="mode-selection">

                <!-- Upload Interface with Drag & Drop -->
                <div class="upload-interface">
                  <div class="upload-area" :class="{
                    'drag-active': isDragActive,
                    'has-file': previewImage
                  }" @drop="handleDrop" @dragover="handleDragOver" @dragenter="handleDragEnter"
                    @dragleave="handleDragLeave" @click="triggerFileInput">
                    <input type="file" accept="image/*" ref="fileInput" @change="handleFileUpload" class="file-input" />

                    <!-- Upload Prompt -->
                    <div v-if="!previewImage" class="upload-prompt">
                      <div class="upload-icon">

                        <svg fill="#00aef0" width="80px" height="80px" viewBox="0 0 35.00 35.00" version="1.1"
                          xmlns="http://www.w3.org/2000/svg" transform="matrix(1, 0, 0, 1, 0, 0)" stroke="#00aef0"
                          stroke-width="0.00035">
                          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                          <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC"
                            stroke-width="0.42000000000000004"></g>
                          <g id="SVGRepo_iconCarrier">
                            <title>upload-1</title>
                            <path
                              d="M29.426 15.535c0 0 0.649-8.743-7.361-9.74-6.865-0.701-8.955 5.679-8.955 5.679s-2.067-1.988-4.872-0.364c-2.511 1.55-2.067 4.388-2.067 4.388s-5.576 1.084-5.576 6.768c0.124 5.677 6.054 5.734 6.054 5.734h9.351v-6h-3l5-5 5 5h-3v6h8.467c0 0 5.52 0.006 6.295-5.395 0.369-5.906-5.336-7.070-5.336-7.070z">
                            </path>
                          </g>
                        </svg>
                      </div>
                      <h3 class="upload-title">
                        {{ isDragActive ? 'Drop image here' : 'Upload Photo' }}
                      </h3>
                      <p class="upload-description">
                        Drag and drop an image here, or
                        <span class="browse-text">browse</span>
                      </p>
                      <p class="upload-info">
                        Supports: JPG, PNG • Max size: 10MB
                      </p>
                    </div>

                    <!-- File Preview -->
                    <div v-else class="file-preview">
                      <img :src="previewImage" alt="Preview" class="preview-image" />
                      <div class="file-details">
                        <p class="file-name">{{ selectedFileName }}</p>
                        <p class="file-size">{{ selectedFileSize }}</p>
                      </div>
                    </div>
                  </div>


                </div>
              </div>
              <h2><span>OR</span></h2>

              <!-- Camera Interface -->
              <div v-if="mode === 'camera'" class="camera-interface">
                <video v-show="cameraActive" ref="video" autoplay playsinline class="camera-preview"></video>

                <div v-if="!cameraActive" class="camera-start">

                  <button @click="startCamera" class="start-camera-btn">
                    <font-awesome-icon :icon="['fas', 'camera']" style="align-self: flex-start;" />
                    <span class="btn-text default-text">Take a Picture</span>
                    <span class="btn-text hover-text">Start Camera</span>
                  </button>

                </div>

                <div v-if="cameraActive" class="camera-controls">
                  <button @click="capturePhoto" class="capture-btn">
                    Capture
                  </button>
                  <button @click="stopCamera" class="cancel-btn">
                    Cancel
                  </button>
                </div>

                <div v-if="cameraError" class="error-message">
                  <p>{{ cameraError }}</p>
                  <button @click="retryCamera" class="retry-btn">
                    Try Again
                  </button>
                </div>
              </div>


              <!-- Preview -->
              <div v-if="previewImage" class="image-preview">
                <img :src="previewImage" alt="Preview" class="preview-img" />
                <button @click="removeImage" class="remove-btn">
                  Remove
                </button>
              </div>
            </div>


            <FaceIdWarning :isVisible="avatarWarning" @close="avatarWarning = false" />
          </form>
          <LoadingAI :isLoading="isLoading" />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <TestBox v-if="step == 2" button-text="Get started" :handleClick="next"
          inside-div-class="third_page flex flex-col justify-center items-center " added-class="w-11/12">
          <GuidesRules />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <TestBox v-if="step == 3" button-text="Get started" :handleClick="next"
          inside-div-class="third_page flex flex-col justify-center items-center " added-class="w-2/5"
          :disableBtn="assesmentSetup">
          <AssessmentSetup />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <TestBox v-if="step == 4" button-text="Next" :handleClick="next" :disableBtn="!allowedCamera"
          inside-div-class="fouth_page flex flex-col justify-center items-center " added-class="w-11/12">
          <CameraSetup :setupWebcam="false" />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <div class="w-full" v-if="step == 5">
          <CodingChallenge v-if="this.assessementStore.isCoding" :challenge="currentChallenge" />
          <ScreenersContainer v-else :nextSection="() => next()" :nextStep="() => step++" />
        </div>
      </Transition>

      <Transition>
        <testContainer v-if="step == 6" :testStart="step == 6" :currentTest="assessementStore.test"
          :qst="assessementStore.question" @next="
            next_qst(
              assessementStore?.assessements[assessementStore.test]
                ?.questions_list?.length
            )
            " />
      </Transition>
      <Transition name="slide-fade">
        <TestBox v-if="step == 7" button-text="Finish" :handleClick="handleRating"
          inside-div-class="third_page flex flex-col justify-center items-center " added-class="w-3/5">
          <FeedBack :handleFeedBack="handleFeedBack" :addRating="addRating" />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <TestBox v-if="step == 8" button-text="Submit" :handleClick="() => {
          this.step++;
          assessementStore.submitNewTalent;
        }
          " inside-div-class=" " added-class="w-3/5">
          <NewTalent />
        </TestBox>
      </Transition>
      <Transition name="slide-fade">
        <section v-if="
          this.assessementStore.projectId == '656dd5e2a26850987a567af8' &&
          this.score > 70
        " class="share_page page_width" v-show="step == 9">
          <ShareResults :nextSection="nextSection" />
        </section>
      </Transition>
      <Transition name="slide-fade">
        <section class="share_page page_width" v-show="this.assessementStore.projectId == '656dd5e2a26850987a567af8' &&
          this.score > 70
          ? step == 10
          : step == 9
          ">
           <div class="thankyou-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" fill="none">
        <path fill="#F5F5F5" d="M403.253 600.94c172.724 0 312.744-8.175 312.744-18.259s-140.02-18.259-312.744-18.259-312.745 8.175-312.745 18.259 140.02 18.259 312.745 18.259"/>
        <path fill="#DBEEFE" d="M661.057 312.379H275.55a14.92 14.92 0 0 1-14.219-9.943 14.9 14.9 0 0 1-.814-6.074l14.13-227.563a17.335 17.335 0 0 1 17.017-16.033h385.507A14.937 14.937 0 0 1 692.205 68.8l-14.13 227.563a17.342 17.342 0 0 1-17.018 16.017"/>
        <path fill="#fff" d="M661.057 312.379H275.55a14.92 14.92 0 0 1-14.219-9.943 14.9 14.9 0 0 1-.814-6.074l14.13-227.563a17.335 17.335 0 0 1 17.017-16.033h385.507A14.937 14.937 0 0 1 692.205 68.8l-14.13 227.563a17.342 17.342 0 0 1-17.018 16.017" opacity=".8"/>
        <path fill="#fff" d="M324.958 118.339h-45.906a4.2 4.2 0 0 1-4.032-2.81 4.2 4.2 0 0 1-.226-1.722l1.306-20.97a4.84 4.84 0 0 1 4.839-4.548h45.906z" opacity=".4"/>
        <path fill="#fff" d="M676.931 56.787H340.798a12.905 12.905 0 0 0-12.759 12.017l-3.226 52.212h-45.922a4.84 4.84 0 0 0-4.839 4.549l-1.21 19.679a4.22 4.22 0 0 0 1.123 3.216 4.2 4.2 0 0 0 3.135 1.332h45.906l-9.097 146.574a11.17 11.17 0 0 0 2.994 8.499 11.162 11.162 0 0 0 8.297 3.518h336.133a12.91 12.91 0 0 0 12.759-12.017l14.13-227.562a11.151 11.151 0 0 0-11.291-12.017"/>
        <path fill="#fff" d="M320.996 181.249h-45.842a4.2 4.2 0 0 1-1.711-.335 4.24 4.24 0 0 1-2.335-2.486 4.2 4.2 0 0 1-.228-1.728l1.225-19.679a4.835 4.835 0 0 1 4.839-4.548h45.906zM319.107 212.702h-45.906a4.238 4.238 0 0 1-4.047-2.821 4.25 4.25 0 0 1-.228-1.728l1.226-19.695a4.836 4.836 0 0 1 4.839-4.532h45.906zM317.145 244.144h-45.906a4.25 4.25 0 0 1-4.042-2.824 4.25 4.25 0 0 1-.233-1.725l1.226-19.695a4.916 4.916 0 0 1 4.839-4.548h45.906zM315.205 275.594h-45.826a4.22 4.22 0 0 1-4.136-2.835 4.2 4.2 0 0 1-.219-1.762l1.226-19.679a4.84 4.84 0 0 1 4.839-4.548h45.922z" opacity=".4"/>
        <path fill="#DBEEFE" d="M278.456 146.121a1.823 1.823 0 0 1-1.855-1.984l1.065-17.275a2.386 2.386 0 0 1 2.322-2.194h38.906a1.824 1.824 0 0 1 1.855 1.984l-1.065 17.292a2.385 2.385 0 0 1-2.323 2.177z" opacity=".2"/>
        <path fill="#AAD6FB" d="M408.086 83.886H352.55a7.21 7.21 0 0 1-7.242-7.726 8.336 8.336 0 0 1 8.194-7.71h55.503a7.195 7.195 0 0 1 7.242 7.71 8.37 8.37 0 0 1-8.161 7.726" opacity=".3"/>
        <path fill="#AAD6FB" d="M672.628 76.172a8.34 8.34 0 0 1-8.194 7.726 7.195 7.195 0 0 1-7.242-7.726 8.32 8.32 0 0 1 8.194-7.71 7.176 7.176 0 0 1 7.242 7.71"/>
        <path fill="#DBEEFE" d="M515.406 215.897H351.638a14.24 14.24 0 0 1-13.584-9.503 14.25 14.25 0 0 1-.772-5.805l4.92-79.601a16.503 16.503 0 0 1 16.243-15.292h163.816a14.22 14.22 0 0 1 13.574 9.49c.655 1.86.916 3.836.766 5.802l-4.936 79.585a16.547 16.547 0 0 1-16.259 15.324m-157.01-109.523a15.79 15.79 0 0 0-15.533 14.63l-4.936 79.585a13.605 13.605 0 0 0 8.233 13.545 13.6 13.6 0 0 0 5.494 1.085h163.8a15.8 15.8 0 0 0 10.612-4.294 15.8 15.8 0 0 0 4.921-10.336l4.952-79.601a13.615 13.615 0 0 0-8.235-13.54 13.6 13.6 0 0 0-5.491-1.09z" opacity=".4"/>
        <path fill="#DBEEFE" d="m371.531 158.634 23.502 15.21a53.2 53.2 0 0 1 18.275-16.565l-12.63-25.002a85.2 85.2 0 0 0-29.147 26.357"/>
        <path fill="#fff" d="m371.531 158.634 23.502 15.21a53.2 53.2 0 0 1 18.275-16.565l-12.63-25.002a85.2 85.2 0 0 0-29.147 26.357" opacity=".8"/>
        <path fill="#263238" d="M357.367 199.369h28.776a50.8 50.8 0 0 1 8.888-25.533l-23.502-15.211a81.1 81.1 0 0 0-14.162 40.744"/>
        <path fill="#AAD6FB" d="M439.321 122.203a80.36 80.36 0 0 0-38.712 10.033l12.63 25.034a50.5 50.5 0 0 1 24.195-6.275 44.94 44.94 0 0 1 42.962 30.028 44.9 44.9 0 0 1 2.412 18.362h28.776c2.774-42.631-29.631-77.182-72.263-77.182"/>
        <path fill="#DBEEFE" d="M420.641 245.184h-84.36a1.375 1.375 0 0 1-1.296-.913 1.4 1.4 0 0 1-.075-.555l.371-5.936c.036-.402.222-.776.521-1.047a1.6 1.6 0 0 1 1.092-.42h84.36z"/>
        <path fill="#DBEEFE" d="M420.641 245.184h106.458a1.61 1.61 0 0 0 1.613-1.468l.371-5.936a1.374 1.374 0 0 0-1.371-1.467H421.254z" opacity=".2"/>
        <path fill="#DBEEFE" d="M477.934 268.586H334.829a1.354 1.354 0 0 1-1.371-1.468l.371-5.92a1.61 1.61 0 0 1 1.613-1.468h143.105z"/>
        <path fill="#DBEEFE" d="M477.93 268.586h47.712a1.611 1.611 0 0 0 1.613-1.468l.371-5.92a1.353 1.353 0 0 0-1.371-1.467h-47.777z" opacity=".2"/>
        <path fill="#DBEEFE" d="M350.182 291.954h-16.808a1.38 1.38 0 0 1-1.005-.436 1.374 1.374 0 0 1-.366-1.032l.371-5.936a1.612 1.612 0 0 1 1.613-1.468h16.808z"/>
        <path fill="#DBEEFE" d="M350.188 291.954H524.23a1.62 1.62 0 0 0 1.613-1.468l.371-5.936a1.36 1.36 0 0 0-.371-1.039 1.37 1.37 0 0 0-1.016-.429H350.736z" opacity=".2"/>
        <path fill="#AAD6FB" d="M562.568 292.685a.6.6 0 0 1-.462-.19.61.61 0 0 1-.167-.471l11.55-185.995a.71.71 0 0 1 .709-.662.62.62 0 0 1 .581.412q.042.121.032.25l-11.549 185.93a.71.71 0 0 1-.694.726"/>
        <path fill="#fff" d="M575.571 154.761a4.834 4.834 0 0 1-4.726 4.452 4.136 4.136 0 0 1-3.952-2.764 4.1 4.1 0 0 1-.225-1.688 4.837 4.837 0 0 1 4.742-4.452 4.114 4.114 0 0 1 4.161 4.452"/>
        <path fill="#DBEEFE" d="M570.8 159.879a4.85 4.85 0 0 1-1.918-.392 4.842 4.842 0 0 1-2.921-4.721 5.533 5.533 0 0 1 5.436-5.129 4.85 4.85 0 0 1 3.528 1.518 4.83 4.83 0 0 1 1.311 3.611 5.52 5.52 0 0 1-5.436 5.113m.548-8.903a4.083 4.083 0 0 0-4.016 3.79 3.52 3.52 0 0 0 .945 2.667 3.5 3.5 0 0 0 2.604 1.108 4.086 4.086 0 0 0 4.016-3.775 3.525 3.525 0 0 0-2.126-3.509 3.5 3.5 0 0 0-1.423-.281"/>
        <path fill="#fff" d="M573.087 194.824a4.84 4.84 0 0 1-4.726 4.452 4.15 4.15 0 0 1-3.067-1.308 4.16 4.16 0 0 1-1.111-3.144 4.84 4.84 0 0 1 4.726-4.484 4.13 4.13 0 0 1 4.178 4.484"/>
        <path fill="#DBEEFE" d="M568.331 200.016a4.84 4.84 0 0 1-4.839-5.113 5.533 5.533 0 0 1 5.436-5.13 4.838 4.838 0 0 1 4.839 5.13 5.515 5.515 0 0 1-5.436 5.113m.549-8.904a4.083 4.083 0 0 0-4.017 3.791 3.504 3.504 0 0 0 3.549 3.774 4.08 4.08 0 0 0 2.741-1.107 4.07 4.07 0 0 0 1.275-2.667 3.52 3.52 0 0 0-.906-2.731 3.5 3.5 0 0 0-2.642-1.14z"/>
        <path fill="#fff" d="m567.419 214.312-5.888 9.211h10.646z"/>
        <path fill="#DBEEFE" d="M572.137 224.209h-10.646a.628.628 0 0 1-.549-.322.68.68 0 0 1 0-.678l5.904-9.21a.67.67 0 0 1 .573-.315.68.68 0 0 1 .572.315l4.839 9.21a.7.7 0 0 1-.051.675.7.7 0 0 1-.594.325zm-9.404-1.322h8.274l-3.709-7.227z"/>
        <path fill="#AAD6FB" d="M642.701 292.685a.6.6 0 0 1-.462-.19.6.6 0 0 1-.133-.218.6.6 0 0 1-.034-.253l11.549-185.995a.713.713 0 0 1 .961-.616q.121.05.211.145a.6.6 0 0 1 .167.471l-11.549 185.93a.726.726 0 0 1-.71.726"/>
        <path fill="#fff" d="M651.118 228.585a4.13 4.13 0 0 0-4.161-4.452 4.84 4.84 0 0 0-4.743 4.452 4.16 4.16 0 0 0 1.112 3.143c.392.419.868.751 1.396.976a4.1 4.1 0 0 0 1.67.333 4.84 4.84 0 0 0 4.726-4.452"/>
        <path fill="#DBEEFE" d="M646.355 233.699a4.83 4.83 0 0 1-3.523-1.513 4.834 4.834 0 0 1-1.316-3.6 5.513 5.513 0 0 1 5.435-5.113 4.84 4.84 0 0 1 3.523 1.513 4.84 4.84 0 0 1 1.316 3.6 5.515 5.515 0 0 1-5.435 5.113m.548-8.904a4.095 4.095 0 0 0-4.016 3.791 3.52 3.52 0 0 0 .944 2.672 3.53 3.53 0 0 0 2.604 1.118 4.096 4.096 0 0 0 4.017-3.79 3.535 3.535 0 0 0-3.549-3.791"/>
        <path fill="#fff" d="M657.353 128.402a4.13 4.13 0 0 0-1.105-3.149 4.13 4.13 0 0 0-3.073-1.303 4.835 4.835 0 0 0-4.742 4.452 4.16 4.16 0 0 0 1.111 3.143 4.15 4.15 0 0 0 3.067 1.308 4.84 4.84 0 0 0 4.742-4.451"/>
        <path fill="#DBEEFE" d="M652.572 133.508a4.74 4.74 0 0 1-4.839-5.113 5.533 5.533 0 0 1 5.436-5.129 4.83 4.83 0 0 1 3.529 1.518 4.84 4.84 0 0 1 1.31 3.611 5.53 5.53 0 0 1-5.436 5.113m.565-8.904a4.083 4.083 0 0 0-4.016 3.791 3.495 3.495 0 0 0 3.548 3.774 4.084 4.084 0 0 0 4.017-3.774 3.51 3.51 0 0 0-2.127-3.509 3.5 3.5 0 0 0-1.422-.282"/>
        <path fill="#fff" d="m649.461 183.754-4.742-9.227h10.629z"/>
        <path fill="#DBEEFE" d="M649.406 184.416a.61.61 0 0 1-.548-.338l-4.758-9.211a.7.7 0 0 1-.084-.33c0-.116.028-.23.084-.331a.7.7 0 0 1 .596-.339h10.646a.63.63 0 0 1 .565.339.7.7 0 0 1 0 .661l-5.872 9.211a.76.76 0 0 1-.629.338m-3.581-9.21 3.71 7.21 4.614-7.21z"/>
        <path fill="#AAD6FB" d="M602.639 292.685a.6.6 0 0 1-.462-.19.6.6 0 0 1-.167-.471l11.549-185.995a.707.707 0 0 1 .71-.661.59.59 0 0 1 .586.409.6.6 0 0 1 .027.252L603.349 291.96a.73.73 0 0 1-.71.725"/>
        <path fill="#fff" d="M614.949 166.144a4.84 4.84 0 0 1-4.742 4.451 4.1 4.1 0 0 1-3.067-1.303 4.123 4.123 0 0 1-1.095-3.148 4.84 4.84 0 0 1 4.726-4.452 4.15 4.15 0 0 1 3.067 1.308 4.14 4.14 0 0 1 1.111 3.144"/>
        <path fill="#DBEEFE" d="M610.168 171.301a4.84 4.84 0 0 1-4.839-5.129 5.52 5.52 0 0 1 5.436-5.113 4.84 4.84 0 0 1 4.839 5.113 5.533 5.533 0 0 1-5.436 5.129m.548-8.92a4.1 4.1 0 0 0-4.016 3.791 3.54 3.54 0 0 0 2.129 3.504c.449.192.932.29 1.42.286a4.097 4.097 0 0 0 4.016-3.79 3.525 3.525 0 0 0-2.126-3.509 3.5 3.5 0 0 0-1.423-.282"/>
        <path fill="#fff" d="M609.409 255.28a4.84 4.84 0 0 1-4.726 4.452 4.13 4.13 0 0 1-4.178-4.452 4.84 4.84 0 0 1 4.726-4.452 4.15 4.15 0 0 1 3.066 1.309 4.14 4.14 0 0 1 1.112 3.143"/>
        <path fill="#DBEEFE" d="M604.636 260.387a4.83 4.83 0 0 1-3.523-1.513 4.834 4.834 0 0 1-1.316-3.6 5.53 5.53 0 0 1 5.436-5.129 4.8 4.8 0 0 1 1.921.394 4.82 4.82 0 0 1 2.636 2.794c.225.621.321 1.281.282 1.941a5.52 5.52 0 0 1-5.436 5.113m.548-8.904a4.09 4.09 0 0 0-2.747 1.113 4.09 4.09 0 0 0-1.269 2.678 3.518 3.518 0 0 0 3.548 3.774 4.08 4.08 0 0 0 4.017-3.774 3.525 3.525 0 0 0-2.127-3.509 3.5 3.5 0 0 0-1.422-.282"/>
        <path fill="#fff" d="m613.685 114.723-5.904 9.226h10.646z"/>
        <path fill="#DBEEFE" d="M618.38 124.607h-10.63a.63.63 0 0 1-.565-.339.7.7 0 0 1 0-.661l5.888-9.211a.7.7 0 0 1 .581-.314.7.7 0 0 1 .58.314l4.742 9.211a.64.64 0 0 1 0 .661.76.76 0 0 1-.596.339m-9.388-1.339h8.323l-3.726-7.13zM179.921 162.609l-1.613 7.855-1.613 8.065c-1.065 5.355-1.968 10.711-2.79 15.969-.42 2.629-.742 5.226-1.017 7.791s-.516 5.049-.645 7.452a65 65 0 0 0 0 6.581c0 1.887.42 3.5.339 3.678l-1.081-1.855c-.484-.484-1.371-.936-1.516-.726-.145.209.419.58 1.452.855 1.329.314 2.684.503 4.048.564a48 48 0 0 0 5.194-.129c3.914-.321 7.808-.86 11.662-1.613 2.016-.387 4.065-.79 6.097-1.242l6.033-1.306.806-.178a8.47 8.47 0 0 1 9.483 4.781 8.472 8.472 0 0 1-2.643 10.285l-3.017 2.226-2.968 2.032c-2.032 1.29-4.081 2.549-6.21 3.71a89 89 0 0 1-13.646 6.097 66 66 0 0 1-7.855 2.113 42.7 42.7 0 0 1-9.178.871 32.5 32.5 0 0 1-11.291-2.161 26.5 26.5 0 0 1-11.614-9.049q-.465-.666-.838-1.387l-.242-.468a45.2 45.2 0 0 1-4.114-12.904 76 76 0 0 1-1.032-10.678 137 137 0 0 1 0-9.84q.177-4.79.661-9.436c.549-6.194 1.468-12.226 2.517-18.21.548-2.984 1.097-5.952 1.774-8.904.323-1.484.645-2.968 1.016-4.452l1.194-4.565a16.885 16.885 0 0 1 20.152-12.166 16.89 16.89 0 0 1 12.737 19.796z"/>
        <path fill="#000" d="m179.921 162.609-1.613 7.855-1.613 8.065c-1.065 5.355-1.968 10.711-2.79 15.969-.42 2.629-.742 5.226-1.017 7.791s-.516 5.049-.645 7.452a65 65 0 0 0 0 6.581c0 1.887.42 3.5.339 3.678l-1.081-1.855c-.484-.484-1.371-.936-1.516-.726-.145.209.419.58 1.452.855 1.329.314 2.684.503 4.048.564a48 48 0 0 0 5.194-.129c3.914-.321 7.808-.86 11.662-1.613 2.016-.387 4.065-.79 6.097-1.242l6.033-1.306.806-.178a8.47 8.47 0 0 1 9.483 4.781 8.472 8.472 0 0 1-2.643 10.285l-3.017 2.226-2.968 2.032c-2.032 1.29-4.081 2.549-6.21 3.71a89 89 0 0 1-13.646 6.097 66 66 0 0 1-7.855 2.113 42.7 42.7 0 0 1-9.178.871 32.5 32.5 0 0 1-11.291-2.161 26.5 26.5 0 0 1-11.614-9.049q-.465-.666-.838-1.387l-.242-.468a45.2 45.2 0 0 1-4.114-12.904 76 76 0 0 1-1.032-10.678 137 137 0 0 1 0-9.84q.177-4.79.661-9.436c.549-6.194 1.468-12.226 2.517-18.21.548-2.984 1.097-5.952 1.774-8.904.323-1.484.645-2.968 1.016-4.452l1.194-4.565a16.885 16.885 0 0 1 20.152-12.166 16.89 16.89 0 0 1 12.737 19.796z" opacity=".2"/>
        <path fill="#263238" d="M139.099 575.167s-15.259-13.839-14.888-18.227l-12.678-6.452-7.549 13.033 7.001 3.92 1.935-.629 1.275 2.21c6.323 3.226 20.13 9.533 24.953 9.355a3.09 3.09 0 0 0-.049-3.21"/>
        <path fill="#263238" d="M140.193 412.314c9.226-66.133 6.21-106.248 15.904-138.815l60.004-4.839s-26.598 104.845-38.712 148.493-50.374 142.622-50.374 142.622l-16.695-8.549s13.63-87.78 29.873-138.912"/>
        <path fill="#000" d="M189.134 317.551c-4.081 9.436-12.049 70.036-12.017 100.296 0-.21.129-.451.194-.677 4.274-15.34 10.355-38.261 16.42-61.504z" opacity=".2"/>
        <path fill="#263238" d="M235.507 577.326s-24.889-5.645-26.034-9.678h-15.791l-1.307 15.05h8.775l1.613-1.387 1.968 1.387c7.097 0 27.614 0 32.034-2.581.032-2.291-1.258-2.791-1.258-2.791"/>
        <path fill="#263238" d="M193.366 414.163c-13.178-89.538-13.178-144.977-13.178-144.977l58.713-.952s-7.049 106.781-9.501 151.51c-2.532 46.502-17.049 148.863-17.049 148.863h-20.389s-4.839-96.941 1.404-154.444"/>
        <path fill="#7F3E3B" d="M186.986 101.086c-2.79 17.566-7.855 26.985-11.291 35.712 5.92 5.516 18.598 12.339 31.389 12.162 12.791-.178 10.469-8.839 7.033-13.307-9.178-4.63-9.533-9.307-7.291-13.404z"/>
        <path fill="#263238" d="M165.047 139.282s8.162-9.597 11.388-11.21 27.63 1.613 33.389 2.774c5.758 1.161 12.775 9.678 12.775 9.678zM227.445 99.625c-.258 1.162.145 2.226.903 2.388.759.161 1.613-.646 1.839-1.807s-.145-2.226-.903-2.387-1.613.58-1.839 1.806"/><path fill="#630F0F" d="M226.975 101.215a42 42 0 0 0 3.323 11.081c-2.484 1.468-5.775-.161-5.775-.161z"/>
        <path fill="#263238" d="M231.412 92.953a.66.66 0 0 1-.645 0 5.47 5.47 0 0 0-5.017.162.68.68 0 0 1-.951-.194.68.68 0 0 1-.106-.524.7.7 0 0 1 .299-.444 6.82 6.82 0 0 1 6.323-.29.69.69 0 0 1 .369.386.7.7 0 0 1-.014.533.62.62 0 0 1-.258.371"/>
        <path fill="#7F3E3B" d="M189.014 87.842c-2.306 14.388-4.403 22.695 1.162 31.534 8.355 13.292 27.421 10.582 33.985-2.871 5.936-12.097 8.727-33.598-3.951-42.244a20.1 20.1 0 0 0-19.349-1.825 20.1 20.1 0 0 0-11.847 15.406"/>
        <path fill="#263238" d="M215.174 101.618c11.646 2.516 8.259-11.872 5.468-13.307 7.001 4.193 11.646-.678 11.291-5.065 5.355-.887 5.081-9.678 1.403-9.952-2.322-6.081-13.178-11.195-15.194-7.436-5.855-6.807-21.534-5.646-19.259-.71-5.952-1.161-18.63-.63-14.388 8.34-9.049 1.16-8.614 16.645-2.71 18.258-6.178 3.42-.194 15.098 2.468 22.421s29.179.807 30.921-12.549"/>
        <path fill="#7F3E3B" d="M208.536 100.147a12.27 12.27 0 0 0 1.903 8.791c2.274 3.226 5.791 1.21 7.162-2.677 1.225-3.484 1.516-9.356-1.726-11.114s-6.743 1.065-7.339 5"/>
        <path fill="#DBEEFE" d="M165.874 137.66c19.84-2.435 47.987-2.226 66.665 1.032a21.247 21.247 0 0 1 17.485 19.227c2.323 28.502-3.984 78.312-9.436 128.46-43.954 14.743-88.57-.21-88.57-.21-.984-10.113 4.468-43.261 7.872-60.955-2.5-18.259-8.84-39.18-12.404-63.456a21.2 21.2 0 0 1 4.128-15.914 21.2 21.2 0 0 1 14.26-8.184"/>
        <path fill="#000" d="M225.57 177.574c3 17.163 12.501 37.615 21.195 51.149 1.194-11.856 2.194-23.147 2.823-33.567z" opacity=".2"/>
        <path fill="#DBEEFE" d="M370.891 137.818a28.66 28.66 0 0 0-39.566 8.823 28.664 28.664 0 0 0 8.823 39.567c-2.871 3.871-11.291 15.243-13.856 19.63-3.226 5.307-4.839 10.081-2.613 11.404s5.613-2.435 8.759-7.742c2.613-4.371 8.613-17.179 10.629-21.566a28.66 28.66 0 0 0 38.337-11.485 28.66 28.66 0 0 0-10.513-38.615zm6.888 37.47a25.07 25.07 0 0 1-25.14 11.979 25.067 25.067 0 0 1-20.024-33.173 25.065 25.065 0 0 1 22.311-16.664c4.95-.261 9.866.952 14.127 3.485a25.04 25.04 0 0 1 11.492 15.376 25.04 25.04 0 0 1-2.766 18.997"/>
        <path fill="#AAD6FB" d="M378.952 175.975a26.43 26.43 0 0 1-26.51 12.658 26.43 26.43 0 0 1-21.838-19.651 26.438 26.438 0 0 1 39.138-29.235 26.43 26.43 0 0 1 12.654 26.506 26.4 26.4 0 0 1-3.444 9.722" opacity=".2"/>
        <path fill="#7F3E3B" d="m337.815 201.079-3.5-3.097a5.421 5.421 0 0 0-4.84-1.225l-8.065 1.838a10.14 10.14 0 0 0-7.097 6.001l-.774 1.871c5.436 5.532 15.388 7.516 15.388 7.516l2.791.871a4.5 4.5 0 0 0 3.474-.211 4.5 4.5 0 0 0 2.284-2.627l1.823-5.468a5.28 5.28 0 0 0-1.484-5.469"/>
        <path fill="#DBEEFE" d="m249.167 152.088 3.226 7.081 3.403 7.226c2.275 4.839 4.678 9.501 7.081 14.066a156 156 0 0 0 3.742 6.629 103 103 0 0 0 3.807 6.113 63 63 0 0 0 3.791 5.033c1.129 1.387 2.29 2.387 2.322 2.549l-2-.823a19.4 19.4 0 0 0 7.033 1.774c3.417.297 6.851.34 10.275.129 3.677-.161 7.468-.548 11.291-1 1.919-.242 3.871-.468 5.823-.758l5.774-.774.791-.113a8.482 8.482 0 0 1 9.194 11.028 8.48 8.48 0 0 1-3.388 4.457l-2.935 1.935-2.887 1.742a140 140 0 0 1-5.952 3.226 86 86 0 0 1-6.194 2.823c-2.113.871-4.291 1.613-6.452 2.403a74.2 74.2 0 0 1-14.356 3.227 45.6 45.6 0 0 1-17.517-1.065 8 8 0 0 1-1.21-.452l-.774-.371a43 43 0 0 1-11.065-7.645 76 76 0 0 1-7.227-7.775 134 134 0 0 1-10.871-15.549c-3.226-5.21-6.033-10.501-8.694-15.808-1.307-2.677-2.613-5.339-3.807-8.065-.597-1.355-1.21-2.726-1.774-4.097l-1.742-4.307a16.888 16.888 0 0 1 31.001-13.387z"/>
        <path fill="#263238" d="M585.281 522.364c-2.484-19.501 8.388-35.228 18.905-50.454 12.372-17.905 25.163-36.406 17.598-61.811a53.25 53.25 0 0 0-10.178-18.985 65.94 65.94 0 0 1-22.179 29.034c-14.033 10.453-30.647 13.743-42.212 8.404-6.533-3-14.517-10.081-15.727-26.663-1.194-16.968 8.662-29.034 26.34-32.26 17.679-3.226 39.148 3.226 53.229 18.405a70.14 70.14 0 0 0 2.968-32.664l1.613-.225a71.34 71.34 0 0 1-3.339 34.26 54.7 54.7 0 0 1 11.033 20.307c7.791 26.131-5.226 44.971-17.824 63.182-10.823 15.662-20.969 30.453-18.63 49.325zM567.006 370.42a50 50 0 0 0-8.887.774c-16.792 3.048-26.147 14.517-25.034 30.534.871 12.501 5.984 21.259 14.807 25.308 11.081 5.097 27.018 1.871 40.583-8.226a64.4 64.4 0 0 0 21.937-29.034 59.337 59.337 0 0 0-43.406-19.356"/>
        <path fill="#AAD6FB" d="M615.48 360.792c-12.775-11.404-14.243-26.453-6.759-29.631 10.372-4.403 14.243 13.92 6.759 29.631M605.787 471.006c-9.129-14.517-9.678-32.615 1.468-33.873 10.146-1.194 12.372 16.001-1.468 33.873"/>
        <path fill="#AAD6FB" d="M615.765 365.579c-1.758-10.823 3.565-18.888 8.565-17.452 6.92 1.984 1.516 12.646-8.565 17.452M590.655 417.895c-5.597-12.065-2.016-23.244 4.307-23.211 8.742.064 5.855 14.307-4.307 23.211M569.176 369.783c-11.517 1.951-20.163-3.678-18.663-9.017 2.081-7.404 13.485-1.661 18.663 9.017"/>
        <path fill="#AAD6FB" d="M569.182 369.776c-7.065-3.774-8.065-9.388-4.452-10.888 5.081-2.081 7.5 4.678 4.452 10.888M624.696 417.774c-2.79-7.533.162-12.42 3.888-11.114 5.178 1.823 2.516 8.469-3.888 11.114M534.09 412.499c-7.985.871-12.001-3.226-9.823-6.452 3.032-4.613 8.839-.403 9.823 6.452M636.439 522.158l-3.806 28.002 1.598.217 3.806-28.002zM631.59 557.275l-1.84 13.537 1.598.218 1.84-13.538zM554.888 510.091l9.339 68.294a4.985 4.985 0 0 0 4.936 4.307h50.422a4.984 4.984 0 0 0 4.936-4.307l9.339-68.294a4.99 4.99 0 0 0-1.187-3.942 5 5 0 0 0-3.748-1.704h-69.198a5 5 0 0 0-3.683 1.74 4.98 4.98 0 0 0-1.156 3.906"/>
      </svg>
    </div>
    <h1 class="thankyou-title">Your Submission was received</h1>
    <p class="thankyou-message">Thank you</p>
    <a href="https://www.go-platform.com" class="thankyou-btn">Back To Home</a>

        </section>
      </Transition>
    </main>
  </div>
</template>

<script>
import testContainer from "../components/assessementcontainer.vue";
import FaceIdWarning from "../components/FaceIdWarning.vue";
import LoadingAI from "../components/LoadingAI.vue";
import LoaderComponent from "./LoaderComponent.vue";
import axios from "axios";
import { useAssessementStore } from "@/store/assessements";
import { BASE_URL } from "../constants/constants.js";
import ToastNotification from "./ToastNotification.vue";
import ShareResults from "./ShareResults.vue";
import TestBox from "./TestBox.vue";
import GuidesRules from "./GuidesRules.vue";
import AssessmentSetup from "./AssessmentSetup.vue";
import CameraSetup from "./cameraSetup.vue";
import WelcomeCard from "./WelcomeCard.vue";
import WelcomeCardpasstest from "./WelcomeCardpasstest.vue";
import FeedBack from "./FeedBack.vue";
import NewTalent from "./NewTalent.vue";
import ScreenersContainer from "./screeners/ScreenersContainer.vue";
import CodingChallenge from "./codingChallenge/CodingChallenge.vue";
import VueCountryCode from "vue-country-code";

export default {
  name: "assessementTest",
  components: {
    WelcomeCard,
    FaceIdWarning,
    ScreenersContainer,
    LoadingAI,
    NewTalent,
    FeedBack,
    CameraSetup,
    AssessmentSetup,
    GuidesRules,
    TestBox,
    testContainer,
    ToastNotification,
    ShareResults,
    LoaderComponent,
    WelcomeCardpasstest,
    CodingChallenge,
    VueCountryCode
  },
  props: {
    candidateProfile: { type: Object },
  },

  setup() {
    const assessementStore = useAssessementStore();
    return { assessementStore };
  },

  data() {
    return {
      mode: 'camera', // 'camera' or 'upload'
      cameraActive: false,
      cameraError: null,
      currentChallenge: null,
      stream: null,
      previewImage: null,
      isLoading: false,
      avatar_url: "",
      testName: "",
      show: true,
      testNum: -1,
      qstList: false,
      userAlreadyPassed: false,
      isVisible: false,
      bgc: "",
      message: "",
      requiredField: {
        first_name: "",
        last_name: "",
        email: "",
        phone: "",
        avatar: "",
      },
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      country_code: "dz",
      avatar: "",
      starOver: 0,
      rating: 0,
      key1: "",
      key2: "",
      key3: "",
      key4: "",
      key5: "",
      key6: "",
      passed: 1,
      animation: false,
      step: 1,
      checkifTalent: false,
      disabled: false,
      isChecked: false,
      qst: 0,
      logged: false,
      section: false,
      allTests: [],
      feedback: "",
      score: "",
      avatarWarning: false,
      company: "",
      jobPosition: "",
      isDragActive: false,
      selectedFile: null
    };
  },
  computed: {
    testInfos() {
      return this.assessementStore.metaInfos;
    },
    initials() {
      return (
        this.first_name.charAt(0) + this.last_name.charAt(0)
      ).toUpperCase();
    },
    full_name() {
      return this.first_name + " " + this.last_name;
    },
    assesmentSetup() {
      return (
        this.assessementStore.english == "" &&
        this.assessementStore.disability == ""
      );
    },
    allowedCamera() {
      return this.assessementStore.allowFace;
    },
    isTalent() {
      return this.assessementStore.isTalent;
    },
    selectedFileName() {
      return this.selectedFile?.name || 'Uploaded Image'
    },
    selectedFileSize() {
      if (!this.selectedFile) return ''
      const size = this.selectedFile.size
      return size > 1024 * 1024
        ? `${(size / (1024 * 1024)).toFixed(2)} MB`
        : `${(size / 1024).toFixed(2)} KB`
    }
  },
  created() {
    const urlParams = new URLSearchParams(window.location.search);
    this.key1 = urlParams.get("email");
    this.key2 = urlParams.get("date");
    this.checkWelcomeCardStatus();
  },
  watch: {
    "assessementStore.isTalent": function (val) {
      console.log({ updatedISTALENTTTTTTTTTTTTTTTTTT: val });
      if (val) {
        this.step = 4;
      }
    },
  },
  methods: {
    // Mode selection
    setMode(mode) {
      if (this.mode === 'camera' && this.cameraActive) {
        this.stopCamera();
      }
      this.mode = mode;
      this.cameraError = null;
    },

    // Camera methods
    async startCamera() {
      try {
        this.cameraError = null;
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: 'user',
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        });

        await this.$nextTick();
        if (!this.$refs.video) throw new Error('Video element not found');

        this.$refs.video.srcObject = stream;
        this.stream = stream;
        this.cameraActive = true;

        this.$refs.video.onloadedmetadata = () => {
          this.$refs.video.play().catch(e => {
            console.warn("Video play error:", e);
          });
        };
      } catch (error) {
        this.handleCameraError(error);
      }
    },

    capturePhoto() {
      const video = this.$refs.video;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      canvas.getContext('2d').drawImage(video, 0, 0);

      this.previewImage = canvas.toDataURL('image/jpeg', 0.9);
      this.stopCamera();
    },

    stopCamera() {
      if (this.stream) {
        this.stream.getTracks().forEach(track => track.stop());
        this.stream = null;
      }
      this.cameraActive = false;
    },

    handleCameraError(error) {
      console.error("Camera error:", error);
      this.stopCamera();

      if (error.name === 'NotAllowedError') {
        this.cameraError = "Camera access denied. Please allow camera permissions.";
      } else if (error.name === 'NotFoundError') {
        this.cameraError = "No camera found. Please check your device.";
      } else {
        this.cameraError = "Could not access camera. Please try again.";
      }
    },

    retryCamera() {
      this.cameraError = null;
      this.startCamera();
    },

    // Upload methods
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    handleFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        this.processFile(file);
      }
    },

    // Drag and drop methods
    handleDragOver(event) {
      event.preventDefault();
    },

    handleDragEnter(event) {
      event.preventDefault();
      this.isDragActive = true;
    },

    handleDragLeave(event) {
      event.preventDefault();
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.isDragActive = false;
      }
    },

    handleDrop(event) {
      event.preventDefault();
      this.isDragActive = false;

      const file = event.dataTransfer.files?.[0];
      if (file && file.type.startsWith('image/')) {
        this.processFile(file);
      }
    },

    processFile(file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (10MB max)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }

      this.selectedFile = file;

      const reader = new FileReader();
      reader.onload = (e) => {
        this.previewImage = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    // Common methods
    removeImage() {
      this.previewImage = null;
      if (this.mode === 'upload') {
        this.$refs.fileInput.value = '';
      }
    },

    getImageFile() {
      if (!this.previewImage) return null;

      if (this.mode === 'upload') {
        return this.$refs.fileInput.files[0];
      } else {
        // Convert dataURL to File object for camera captures
        return this.dataURLtoFile(this.previewImage, 'capture.jpg');
      }
    },

    dataURLtoFile(dataurl, filename) {
      const arr = dataurl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }

      return new File([u8arr], filename, { type: mime });
    },
    nextSection() {
      this.step++;
      window.scrollTo({ top: 0, behavior: "smooth" });
    },
    checkWelcomeCardStatus() {
      const hasSeenWelcomeCard = localStorage.getItem("hasSeenWelcomeCard");
      if (hasSeenWelcomeCard) {
        //this.show = false;
      }
    },
    start() {
      window.scrollTo({ top: 0, behavior: "smooth" });

      this.show = false;
      // localStorage.setItem('hasSeenWelcomeCard', 'true');
    },
    handleAvatarUpload(event) {
      this.avatar = event.target.files[0];
      // Check if the avatar meets the criteria
      //  this.checkAvatar();
    },
    async checkAvatar() {
      if (!this.avatar) {
        this.errorMessage = "Please select an image file.";
        return;
      }

      try {
        let fileToUpload;

        // Check if the file is already a PNG
        // if (this.avatar.type === 'image/png') {
        //   fileToUpload = this.avatar;
        //  } else {
        // Convert the image to PNG format if it's not already PNG
        fileToUpload = await this.convertToPNG(this.avatar);
        // }

        const formData = new FormData();
        formData.append("avatar", fileToUpload); // Use the appropriate file

        const response = await axios.post(
          `${BASE_URL}/face-detection/check-avatar`,
          formData,
          {
            withCredentials: true,
          }
        );

        console.log("Face detected?", response.data.detected);
        this.avatarWarning = !response.data.detected;
      } catch (error) {
        console.error("Error uploading image:", error);
        this.errorMessage = "There was an error processing the image.";
        this.avatarWarning = true;
      }
    },

    // Method to convert the image to PNG format
    convertToPNG(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (e) => {
          const img = new Image();
          img.src = e.target.result;

          img.onload = () => {
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");

            // Set canvas size to the image dimensions
            canvas.width = img.width;
            canvas.height = img.height;

            // Draw the image on the canvas
            ctx.drawImage(img, 0, 0);

            // Convert the canvas content to a PNG blob
            canvas.toBlob((blob) => {
              if (blob) {
                const pngFile = new File(
                  [blob],
                  `${file.name.split(".")[0]}.png`,
                  {
                    type: "image/png",
                  }
                );
                resolve(pngFile);
              } else {
                reject(new Error("Error converting image to PNG."));
              }
            }, "image/png");
          };

          img.onerror = () => reject(new Error("Error loading image."));
        };

        reader.onerror = () => reject(new Error("Error reading file."));
        reader.readAsDataURL(file);
      });
    },
    /* async checkAvatar() {
       if (!this.avatar) return;
 
       try {
         // Load the face-api models
         await faceapi.nets.ssdMobilenetv1.loadFromUri("/models");
         await faceapi.nets.faceRecognitionNet.loadFromUri("/models");
         await faceapi.nets.faceLandmark68Net.loadFromUri("/models");
 
         // Convert uploaded image to HTMLImageElement
         const img = await faceapi.bufferToImage(this.avatar);
 
         const detection = await faceapi
           .detectSingleFace(img)
           .withFaceLandmarks()
           .withFaceDescriptor();
 
         // Check if a face is detected
         if (detection) {
           // Face detected
           console.log("Face detected:", detection);
 
           // Do something with the detection, if needed
         } else {
           // No face detected
           console.log("No face detected in the provided image");
           this.avatarWarning = true;
         }
       } catch (error) {
         // Handle any errors that occur during face detection
         console.error("Error during face detection:", error);
         this.avatarWarning = true;
       }
     },*/
    async submitForm() {
      /*await this.checkAvatar();
      if (this.avatarWarning) {
        this.isLoading = false;
        return;
      }*/

      this.assessementStore.fetchAssessments().then(() => {
        /*if (this.email !== this.assessementStore.userEmail) {
          this.userAlreadyPassed = true;
          this.isLoading = false;
          this.message =
            "Please enter the email address you received the assessment at!";
          this.bgc = "red";
          this.isVisible = true;
          // console.log({ visibility: this.isVisible });
          setTimeout(() => {
            this.isVisible = false;
          }, 5000);
          return;
        } else {
          */

        try {
          const candidateData = {
            First_name: this.first_name,
            Last_name: this.last_name,
            Email: this.email,
            Phone: this.phone,
            projectId: this.assessementStore.projectId,
          };
          this.assessementStore.setFullName(this.first_name, this.last_name);
          this.assessementStore.setUser(this.email);
          if(this.email === "") {
            this.assessementStore.setUser(this.assessementStore.userEmail);
          }
          this.isLoading = true;
          // this.assessementStore.userLogged()
          var formdata = new FormData();
          const candidateJSON = JSON.stringify(candidateData);
          formdata.append("candidate", candidateJSON);
          formdata.append("Avatar", this.avatar);
          var requestOptions = {
            method: "POST",
            body: formdata,
            redirect: "follow",
          };

          fetch(
            `${BASE_URL}/candidates/post`,
            requestOptions
          )
            .then((response) => {
              // console.log(response.status)
              /*if (response.status == 401) {
                this.userAlreadyPassed = true;
                this.message = "Please enter a valid email address";
                this.bgc = "red";
                this.isVisible = true;
                this.isLoading = false;
                // console.log({ visibility: this.isVisible });
                setTimeout(() => {
                  this.isVisible = false;
                }, 5000);
                return;
              }*/
              return response.text();
            })
            .then((result) => {
              if (!result) return;
              this.step++;
              this.isLoading = false;
              const resultObject = JSON.parse(result);
              if (resultObject.logged) this.assessementStore.userLogged();
              this.disabled = true;
              // this.logged = resultObject.logged;
              // this.$emit("logged_candidate", {
              //   initials: this.initials,
              //   full_name: this.full_name,
              //   logged: this.logged,
              // });
            })
            .then(() => {
              this.assessementStore.getAvatar();
            });
        } catch (error) {
          console.error(error);
          this.isLoading = false;
        }
        //}
      });
    },

    // Auto-save candidate data after step 1 completion
    async autoSaveCandidateData() {
      try {
        await this.assessementStore.fetchAssessments();
        
        const candidateData = {
          First_name: this.first_name,
          Last_name: this.last_name,
          Email: this.email || this.assessementStore.userEmail,
          Phone: this.phone,
          projectId: this.assessementStore.projectId,
          autoSave: true, // Flag to indicate this is an auto-save
        };

        // Set store data immediately
        this.assessementStore.setFullName(this.first_name, this.last_name);
        this.assessementStore.setUser(candidateData.Email);

        const formdata = new FormData();
        const candidateJSON = JSON.stringify(candidateData);
        formdata.append("candidate", candidateJSON);
        
        // Get image file from the media chooser
        const imageFile = this.getImageFile();
        if (imageFile) {
          formdata.append("Avatar", imageFile);
        }

        const requestOptions = {
          method: "POST",
          body: formdata,
          redirect: "follow",
        };

        const response = await fetch(`${BASE_URL}/candidates/post`, requestOptions);
        const result = await response.text();
        
        if (result) {
          const resultObject = JSON.parse(result);
          if (resultObject.logged) {
            this.assessementStore.userLogged();
          }
          //console.log("Candidate data auto-saved successfully");
        }
      } catch (error) {
        console.error("Auto-save failed:", error);
      }
    },
    disable() {
      this.disabled = false;
    },
    SubmitTalent() { },
    handleFeedBack(feedBack) {
      this.feedback = feedBack;
    },
    addRating(rating) {
      switch (rating) {
        case "No":
          this.rating = 1;
          break;

        case "Somewhat":
          this.rating = 3;
          break;

        case "Yes":
          this.rating = 5;
          break;
        default:
          this.rating = 0;
          break;
      }
    },

    async next() {
      if (!this.section) {
        if (this.step === 1) {
          this.isLoading = true;
          this.disabled = false;

          //this.submitForm();
          if (!this.$refs.form.checkValidity()) {
            this.requiredField = {
              first_name: "",
              last_name: "",
              email: "",
              phone: "",
            };
            for (const input of this.$refs.form.elements) {
              if (input.nodeName === "INPUT" && !input.checkValidity()) {
                this.requiredField[input.name] = input.validationMessage;
                this.isLoading = false;
              }
            }
          } else {
            console.log("submiting form");
            await this.submitForm();
            
            // Auto-save candidate data immediately after step 1 completion
            // This ensures the name is saved even if the candidate abandons the assessment later
            await this.autoSaveCandidateData();
          }
          //this.isLoading = false;
        } else {
          this.step++;
          console.log("executing full screen ");
        }
        if (this.step === 2) {
          this.disabled = true;
        }
        // if (this.step === 6 /* && this.assessementStore.question > 0 */) {
        //   console.log('sttep 66')
        //   this.qstList = false
        //   // console.log('answewr', this.assessementStore.answer)
        //   // this.startTimer = !this.startTimer;
        // }
      }
      if (this.step === 5) {
        //this.assessementStore.showAssessmentNameInNavbar = true;
        this.assessementStore.requestFullScreenOnClick();
      }
      if (this.step === 6) {
        this.assessementStore.showAssessmentNameInNavbar = true;
        this.assessementStore.requestFullScreenOnClick();

        // this.testName= this.testInfos[this.testNum].name
        // this.testNum++
        // this.assessementStore.nextTest()
        //this.assessementStore.nextQst()// this.qst++;
        // console.log(
        //   "count",
        //   this.assessementStore.assessements[this.assessementStore.test]
        //     .questions_list.length
        // );
        // await this.assessementStore.fetchAssessments();

        // const alltest = this.assessementStore.assessements;
        // this.assessementStore.nextTest();
        this.assessementStore.resetPreparation();
        this.assessementStore.countDown();

        console.log({
          HERETERTERTERTEDFSADASDFDF:
            this.testInfos[this.assessementStore.test],
        });
        this.next_qst(this.testInfos[this.assessementStore.test].qstCount);
        // this.next_qst(
        //   this.assessementStore.assessements[this.assessementStore.test]
        //     .questions_list.length
        // );
        this.section = true;
        this.qstList = true;
      }
      window.scrollTo({ top: 0, behavior: "smooth" });
    },
    handleRating() {
      this.step++;
      try {
        let evalutaion = JSON.stringify({
          rating: this.rating,
          feedback: this.feedback,
          candidateEmail: this.assessementStore.userEmail,
          projectId: this.assessementStore.projectId,
        });
        let config = {
          method: "post",
          maxBodyLength: Infinity,
          url: `${BASE_URL}/AssessmentTest/candidateRating`,
          headers: {
            "Content-Type": "application/json",
          },
          data: evalutaion,
        };

        axios
          .request(config)

          .then(() => {
            return;
          })
          .catch((error) => {
            console.log(error);
          });
      } catch (error) {
        console.log(error);
      }
    },

    next_qst(length) {
      this.assessementStore.nextQst();
      if (this.assessementStore.question === length + 1)
        if (this.assessementStore.test === this.testInfos.length - 1) {
          this.step++;
          this.assessementStore.testStarted = false;

          // this.submitResult();
        } else {
          // this.assessementStore.resetPreparation()
          this.assessementStore.resetQst();
          this.assessementStore.showAssessmentNameInNavbar = false;
          this.assessementStore.nextTest();
          this.assessementStore.testStarted = false;
        }
      if (this.step === 7 /* && this.assessementStore.question > 0 */) {
        this.submitResult();
        if (this.assessementStore.isTalent) {
          this.step = 9;
        }
        this.qstList = false;
        // this.startTimer = !this.startTimer;
      }
    },
    submitResult() {
      try {
        // this.assessementStore.stopTime();
        this.disabled = !this.disabled;

        // Include constants into the data body
        let dataBody = {
          answers: this.assessementStore.answer,
          exitedTab: this.assessementStore.tabExited,
          fullScreenExited: this.assessementStore.fullScreenExited,
          mouseExited: this.assessementStore.mouseExited,
          exitCount: this.assessementStore.exitedCount,
          screens: this.assessementStore.screenShots,
          isTalent: this.assessementStore.isTalent,
        };

        let answers = JSON.stringify(dataBody);

        let config = {
          method: "post",
          maxBodyLength: Infinity,
          url: `${BASE_URL}/AssessmentTest/evaluateCandidate`,
          headers: {
            "Content-Type": "application/json",
          },
          withCredentials: true,
          data: answers,
        };

        axios
          .request(config)

          .then((response) => {
            this.score = response.data.score;
            this.disabled = !this.disabled;
            if (this.assessementStore.isCheater == true) {
              this.assessementStore.createCheater();
            }
          })
          .catch((error) => {
            console.log(error);
            this.disabled = !this.disabled;
          });
      } catch (error) {
        console.log(error);
      }
    },
    setRating: function (rating) {
      if (this.rating === rating) {
        this.rating = 0;
      } else {
        this.rating = rating;
      }
    },
    fill(rating) {
      this.starOver = rating;
    },
    onSelect({ dialCode }) {
      this.phone = `+${dialCode}`;
    },
  },
  beforeDestroy() {
    this.stopCamera();
  },
  async mounted() {
    await this.assessementStore.fetchAssessments();
    // console.log(this.assessementStore.challenge);
    if (this.assessementStore.isCoding) {
      this.currentChallenge = this.assessementStore.challenge;

    }
    !this.assessementStore.isTalent &&
      axios
        .post(
          `${BASE_URL}/inviteCandidate/candidate`,
          {},
          {
            withCredentials: true,
          }
        )
        .then((response) => {
          this.data = response.data;
          this.key1 = response.data.email;
          this.key2 = response.data.date;
          this.key3 = response.data.exp;
          this.key4 = response.data.deadline;
          this.key5 = response.data.ass;
          this.animation = false;
          this.jobPosition = response.data.jobPosition;
          this.company = response.data.company;
          if (response.data.pass) {
            this.passed = 2;
            this.key6 = response.data.creat;
          }
        })
        .catch((error) => {
          console.error("There was an error!", error);
        });
  },
};
</script>

<style lang="scss" scoped>
.window {
  height: fit-content;
  width: 100%;
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  font-family: "Roboto" !important;
  // padding-top: 3rem;
}

h2 {
  width: 100%;
  text-align: center;
  border-bottom: 1px dashed #00aef0;
  line-height: 0.1em;
  margin: 10px 0 20px;
}

h2 span {
  background: #fff;
  padding: 0 10px;
}

.container {
  font-family: "Poppins";
  font-style: normal;
  padding: 3rem 5rem;
  // padding-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
  max-height: 100vh;

  .required {
    &::after {
      content: "*";
      color: #ff6969;
    }
  }

  .err_msg {
    color: #ff6969;
    font-size: 12px;
    font-weight: 300;
  }

  .stepper {
    display: flex;
    justify-content: space-between;
    width: 65%;
    position: relative;
    margin: 2rem auto;

    .step {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 18%;
      gap: 1rem;

      /*-&-active {
        color:#3361FF;
        background-color: #3361FF;
      }

      &-done {
        background-color: #3361FF;
        color: #3361FF;
                
      }*/
      &-text {
        font-weight: 500;
        font-size: 15.0373px;
        line-height: 23px;
        color: #6b7a99;
        flex-grow: 1;

        &-active {
          color: #00aef0;
        }
      }

      &-number {
        vertical-align: middle;
        display: flex;
        align-items: center;
        background: #6b7a99;
        // z-index: 2;
        border-radius: 50%;
        width: 1rem;
        height: 1rem;
        line-height: 20px;
        font-size: 16px;
        position: relative;
        border: 1rem solid #fff;

        &::after {
          z-index: -1;
          content: "";
          height: 2.82px;
          background: #6b7a99;
          position: absolute;
          margin: 0 2rem;
          width: 13vw;
          border-radius: 50px;
        }

        &-done {
          background-color: #00aef0;
          color: #00aef0;
          display: flex;
          align-items: center;

          &::after {
            z-index: -1;
            content: "";
            height: 2.82px;
            background: #00aef0;
            position: absolute;
            margin: 0 2rem;
            width: 13vw;
            border-radius: 50px;
          }
        }

        &-active {
          color: #00aef0;
          background-color: #00aef0;
          box-shadow: 0 0 0 1rem #eff0f7;
        }
      }
    }

    > :last-child {
      .step-number::after {
        display: none;
      }
    }
  }

  .first_page {
    display: flex;
    flex-direction: column;
    padding: 20px 29px;
    background: white;
    border-radius: 12px;
    width: 90%;

    > :first-child {
      font-weight: 700;
      font-size: 28px;
      text-align: center;
      color: #00aef0;
    }
  }

  .second_page {
    background: #fff;
    border-radius: 10px;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      width: 100%;

      .input_group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        height: fit-content;

        input {
          border: 1px solid #bdbdbd;
          border-radius: 4px;
          height: 3rem;
          width: 100%;
          box-sizing: border-box;
          padding-left: 1rem;
         
        }
      }

      .input_group2 {
        display: flex;
        justify-content: space-between;
        gap: 5rem;
        box-sizing: border-box;

        div {
          width: 100%;
        }
      }

      .input_group3 {
        display: flex;
        align-items: center;
        gap: 1rem;
        box-sizing: border-box;
      }
    }
  }

  .third_page {
    width: 60%;
    background: #fff;
    padding: 2rem;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    line-height: 28px;

    > :first-child {
      font-weight: 600;
      font-size: 28px;
      line-height: 40px;
    }

    > :nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      line-height: 28px;
      border: 1px solid #bdbdbd;
      border-radius: 5px;
    }

    > :nth-child(3) {
      a {
        color: #00aef0;
        text-decoration-line: none;
      }
    }

    > :nth-child(4) {
      display: flex;
      flex-direction: row;
    }
  }

  .fourth_page {
    display: flex;
    flex-direction: column;
    background: #fff;
    align-items: center;
    gap: 2rem;
    border-radius: 20px;

    > :first-child {
      border: 3px solid #00aef0;
      border-radius: 50%;
      color: #00aef0;
      font-size: 48px;
      font-weight: 900;
      width: 5rem;
      height: 5rem;
      text-align: center;
    }

    > :nth-child(2) {
      font-weight: 500;
      font-size: 22px;
      line-height: 28px;
      text-align: center;
    }

    > :nth-child(3) {
      font-weight: 700;
      font-size: 50px;
      line-height: 50px;
      text-align: center;
    }
  }

  .rating_page {
    margin-top: 5%;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    align-items: center;

    > :first-child {
      padding: 2%;
      gap: 0.5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 20px;

      > :first-child {
        font-weight: 600;
        font-size: 41px;
        line-height: 55px;
        text-align: center;
        color: #00aef0;
        width: 80%;
      }

      > :nth-child(2) {
        font-weight: 400;
        font-size: 22px;
        line-height: 33px;
        color: #bbbbbb;
      }

      > :nth-child(4) {
        resize: none;
        outline: none;
        border: none;
        font-weight: 400;
        font-size: 18px;
        line-height: 27px;
        color: #0000004f;
        border-radius: 20px;
        padding: 2%;
      }

      .rating {
        display: flex;
        flex-direction: row;
        justify-content: center;
        width: 100%;
        gap: 1.5rem;
      }

      .rating span {
        display: flex;
        position: relative;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 25px;
        z-index: 2;
      }

      .rating span:before {
        content: "\2605";
        position: absolute;
        color: #00aef0;
        font-size: 43px;
        z-index: -1;
      }

      .rating span:hover {
        color: #00aef0;
      }

      .filled {
        color: #00aef0 !important;
      }
    }
  }

  .more_jobs_btn {
    background: #00aef0;
    color: #fff;
    font-weight: 500;
    font-size: 25px;
    line-height: 38px;
    border: none;
    border-radius: 7px;
    margin: 3rem;
    padding: 0.5rem 2rem;
  }

  .result_page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    button {
      margin: 0;
    }
  }

  .page_width {
    width: 65%;
    padding: 3%;
  }

  .slide-fade-enter-active {
    transition: all 0.6s ease-in-out;
  }

  .slide-fade-leave-active {
    display: none;
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter,
  .slide-fade-leave-to {
    transform: translateX(200px);
    opacity: 0;
  }
}

//media query
@media (max-width: 557px) {
  .container {
    font-family: "Poppins";
    font-style: normal;
    padding: 0rem 0rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
    height: 100%;
    max-height: 100vh;

    .required {
      &::after {
        content: "*";
        color: #ff6969;
      }
    }

    .err_msg {
      color: #ff6969;
      font-size: 12px;
      font-weight: 300;
    }

    .stepper {
      display: flex;
      justify-content: space-between;
      width: 65%;
      position: relative;
      margin: 2rem auto;

      .step {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        width: 18%;
        gap: 1rem;

        &-text {
          font-weight: 500;
          font-size: 15.0373px;
          line-height: 23px;
          color: #6b7a99;
          flex-grow: 1;

          &-active {
            color: #00aef0;
          }
        }

        &-number {
          vertical-align: middle;
          display: flex;
          align-items: center;
          background: #6b7a99;
          border-radius: 50%;
          width: 1rem;
          height: 1rem;
          line-height: 20px;
          font-size: 16px;
          position: relative;
          border: 1rem solid #fff;

          &::after {
            z-index: -1;
            content: "";
            height: 2.82px;
            background: #6b7a99;
            position: absolute;
            margin: 0 2rem;
            width: 13vw;
            border-radius: 50px;
          }

          &-done {
            background-color: #00aef0;
            color: #00aef0;
            display: flex;
            align-items: center;

            &::after {
              z-index: -1;
              content: "";
              height: 2.82px;
              background: #00aef0;
              position: absolute;
              margin: 0 2rem;
              width: 13vw;
              border-radius: 50px;
            }
          }

          &-active {
            color: #00aef0;
            background-color: #00aef0;
            box-shadow: 0 0 0 1rem #eff0f7;
          }
        }
      }

      > :last-child {
        .step-number::after {
          display: none;
        }
      }
    }

    .first_page {
      display: flex;
      flex-direction: column;
      padding: 20px 29px;
      background: white;
      border-radius: 12px;
      width: 95%;

      > :first-child {
        font-weight: 700;
        font-size: 28px;
        text-align: center;
        color: #00aef0;
      }
    }

    .second_page {
      background: #fff;
      border-radius: 10px;
      margin-bottom: 0;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;

      form {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        width: 100%;

        .input_group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          height: fit-content;

          input {
            border: 1px solid #bdbdbd;
            border-radius: 4px;
            height: 3rem;
            width: 100%;
            box-sizing: border-box;
          }
        }

        .input_group2 {
          display: flex;
          justify-content: space-between;
          gap: 5rem;
          box-sizing: border-box;

          div {
            width: 100%;
          }
        }

        .input_group3 {
          display: flex;
          align-items: center;
          gap: 1rem;
          box-sizing: border-box;
        }
      }
    }

    .third_page {
      width: 60%;
      background: #fff;
      padding: 2rem;
      border-radius: 20px;
      display: flex;
      flex-direction: column;
      line-height: 28px;

      > :first-child {
        font-weight: 600;
        font-size: 28px;
        line-height: 40px;
      }

      > :nth-child(2) {
        font-weight: 400;
        font-size: 14px;
        line-height: 28px;
        border: 1px solid #bdbdbd;
        border-radius: 5px;
      }

      > :nth-child(3) {
        a {
          color: #00aef0;
          text-decoration-line: none;
        }
      }

      > :nth-child(4) {
        display: flex;
        flex-direction: row;
      }
    }

    .fourth_page {
      display: flex;
      flex-direction: column;
      background: #fff;
      align-items: center;
      gap: 2rem;
      border-radius: 20px;

      > :first-child {
        border: 3px solid #00aef0;
        border-radius: 50%;
        color: #00aef0;
        font-size: 48px;
        font-weight: 900;
        width: 5rem;
        height: 5rem;
        text-align: center;
      }

      > :nth-child(2) {
        font-weight: 500;
        font-size: 22px;
        line-height: 28px;
        text-align: center;
      }

      > :nth-child(3) {
        font-weight: 700;
        font-size: 50px;
        line-height: 50px;
        text-align: center;
      }
    }

    .rating_page {
      margin-top: 5%;
      padding: 0 !important;
      display: flex;
      flex-direction: column;
      align-items: center;

      > :first-child {
        padding: 2%;
        gap: 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 20px;

        > :first-child {
          font-weight: 600;
          font-size: 41px;
          line-height: 55px;
          text-align: center;
          color: #00aef0;
          width: 80%;
        }

        > :nth-child(2) {
          font-weight: 400;
          font-size: 22px;
          line-height: 33px;
          color: #bbbbbb;
        }

        > :nth-child(4) {
          resize: none;
          outline: none;
          border: none;
          font-weight: 400;
          font-size: 18px;
          line-height: 27px;
          color: #0000004f;
          border-radius: 20px;
          padding: 2%;
        }

        .rating {
          display: flex;
          flex-direction: row;
          justify-content: center;
          width: 100%;
          gap: 1.5rem;
        }

        .rating span {
          display: flex;
          position: relative;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 25px;
          z-index: 2;
        }

        .rating span:before {
          content: "\2605";
          position: absolute;
          color: #00aef0;
          font-size: 43px;
          z-index: -1;
        }

        .rating span:hover {
          color: #00aef0;
        }

        .filled {
          color: #00aef0 !important;
        }
      }
    }

    .more_jobs_btn {
      background: #00aef0;
      color: #fff;
      font-weight: 500;
      font-size: 25px;
      line-height: 38px;
      border: none;
      border-radius: 7px;
      margin: 3rem;
      padding: 0.5rem 2rem;
    }

    .result_page {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      button {
        margin: 0;
      }
    }

    .page_width {
      width: 65%;
      padding: 3%;
    }

    .slide-fade-enter-active {
      transition: all 0.6s ease-in-out;
    }

    .slide-fade-leave-active {
      display: none;
      transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
    }

    .slide-fade-enter,
    .slide-fade-leave-to {
      transform: translateX(200px);
      opacity: 0;
    }
  }
}

.btns_ {
  display: flex;
  justify-content: center;
  gap: 3rem;
  align-items: center;
  margin-top: 3rem;
}

.next_btn {
  background: #00aef0;
  color: #fff;
  border-radius: 7px;
  border: none;
  padding: 1rem;
  font-weight: 600;
  font-size: 25px;
  line-height: 38px;
  width: 7em;
  cursor: pointer;
}

.loading-btn {
  opacity: 0.85;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: not-allowed;
}

.next_btn:hover {
  opacity: 0.85;
}

.back_btn {
  background: #fff;
  color: #00aef0;
  border-radius: 7px;
  border: none;
  padding: 1rem;
  font-weight: 600;
  font-size: 25px;
  line-height: 38px;
  width: 7em;
  top: 95%;
  cursor: pointer;
}

.disabled {
  background: #00aef0;
  cursor: not-allowed;
}

.qst_stepper {
  display: flex;
  justify-content: space-between;
  width: 65%;
  position: relative;

  .step {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 18%;
    gap: 1rem;

    &-number {
      vertical-align: middle;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      width: 1.5rem;
      height: 1.5rem;
      line-height: 20px;
      font-size: 16px;
      position: relative;
      background: #6b7a99;
      color: #fff;
      box-shadow: 0 0 0 1rem #eff0f7;

      &::after {
        z-index: -1;
        content: "";
        height: 2.82px;
        background: #6b7a99;
        position: absolute;
        border-radius: 50px;
        left: calc(100%);
        width: calc(100% * 6);
      }

      &-done {
        background-color: #00aef0;
        color: #fff;
        display: flex;
        align-items: center;
        box-shadow: none;

        &::after {
          z-index: -1;
          content: "";
          height: 2.82px;
          background: #00aef0;
          position: absolute;
          border-radius: 50px;
          left: calc(100%);
          width: calc(100% * 6);
        }
      }

      &-active {
        color: #fff;
        background-color: #00aef0;
        box-shadow: 0 0 0 1rem #fff;
      }
    }
  }

  > :last-child {
    .step-number::after {
      display: none;
    }
  }
}

input[type="file"] {
  padding: 4px;
  margin: -4px;
  position: relative;
  outline: none;
  width: 100%;

  /* File Selector Button Styles */
  &::file-selector-button {
    border-radius: 4px;
    padding: 0 16px;
    height: 40px;
    cursor: pointer;
    background-color: white;
    border: 1px solid rgba(#000, 0.16);
    box-shadow: 0px 1px 0px rgba(#000, 0.05);
    margin-right: 16px;
    width: 25%;
    /*
      This is a hack to change the button label. 
      I'm hiding the default label and then 
      manually applying the width based on 
      updated icon and label.
    */
    color: transparent;

    /*
      Firefox doesn't support the pseudo ::before 
      or ::after elements on this input field so 
      we need to use the @supports rule to enable 
      default styles fallback for Firefox.
    */
    @supports (-moz-appearance: none) {
      color: var(--primary-color);
    }

    &:hover {
      background-color: #f3f4f6;
    }

    &:active {
      background-color: #e5e7eb;
    }
  }

  /* Faked label styles and icon */
  &::before {
    position: absolute;
    pointer-events: none;
    top: 50%;
    transform: translateY(-50%);
    left: 16px;
    height: 20px;
    width: 20px;
    content: "";
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%232196f3'%3E%3Cpath d='M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zM7 9l1.41 1.41L11 7.83V16h2V7.83l2.59 2.58L17 9l-5-5-5 5z'/%3E%3C/svg%3E");
  }

  &::after {
    position: absolute;
    pointer-events: none;
    left: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    content: "Upload Photo (.png)";
  }

  /* Handle Component Focus */
  &:focus-within::file-selector-button,
  &:focus::file-selector-button {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.input_file {
  margin-top: 1rem;
  border: solid 1px #bdbdbd;
  border-radius: 4px;
  padding: 0.25rem;
}

.intructions {
  margin-bottom: 20px;
  font-size: 15px;
}

.loader {
  border: 4px solid #00aef0;
  border-left-color: transparent;
  width: 30px;
  height: 30px;
  animation: spin89345-40df972a 1s linear infinite;
  border-radius: 50%;
}

.media-chooser {
  max-width: 500px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .media-chooser {
    max-width: 100%;
    width: 100%;
    padding: 0 0.5rem;
  }
    .upload-interface {
    height: 70%;
  
    }
}

@media (max-width:441px){
  .upload-icon svg{
    height:20px;
    width:20px;
  }
  .upload-title{
    font-size: 1rem;
  }

}
.mode-selection {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.mode-selection button {
  flex: 1;
  padding: 10px;
  background: #f0f0f0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.mode-selection button.active {
  background: #4285f4;
  color: white;
}

.camera-preview {
  width: 100%;
  background: #000;
  border-radius: 8px;
  margin-bottom: 10px;
}

.camera-controls,
.camera-start {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.start-camera-btn {
  display: inline-flex;
  align-items: center;

  justify-content: start;
  gap: 12px;

  position: relative;
  overflow: hidden;

  width: 500px;
  padding: 12px 24px;

  background-color: #00aef0;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;

  cursor: pointer;

  transition: background-color 0.3s ease, transform 0.2s ease;
}

.start-camera-btn .btn-text {
  position: absolute;
  left: 50%;
  top: 50%;
  /* Added to center vertically */
  transform: translate(-50%, -50%);
  /* Adjusted for vertical centering */
  white-space: nowrap;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.start-camera-btn .default-text {
  opacity: 1;

}

.start-camera-btn .hover-text {
  opacity: 0;
  transform: translate(-50%, 100%);
  /* This will need to be re-evaluated for the hover effect */
}

.start-camera-btn:hover .default-text {
  opacity: 0;
  transform: translate(-50%, -100%);

}

.start-camera-btn:hover .hover-text {
  opacity: 1;
  transform: translate(-50%, -50%);

}


.start-camera-btn .fa-camera {
  /* Assuming .fa-camera is the class applied by font-awesome-icon */
  align-self: center;
  /* Changed from 'flex-start' to 'center' */
}

.capture-btn {
  background: #00aef0;
  color: white;
}

.capture-btn:hover {
  background: white;
  color: #00aef0;
}

.cancel-btn {
  background: #f0f0f0;
}

.cancel-btn:hover {
  background: red;
  color: white;
}

.upload-btn {
  background: #34a853;
  color: white;
}

.remove-btn {
  background: #ea4335;
  color: white;
  margin-top: 10px;
}


.error-message {
  color: #d32f2f;
  background: #ffebee;
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
}

.retry-btn {
  background: #fb8c00;
  color: white;
  margin-top: 8px;
}

.image-preview {
  margin-top: 20px;
  text-align: center;
}

.preview-img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
}

.file-input {
  display: none;
}

.upload-interface {
  width: 100%;
  
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.upload-area:hover {
  border-color: #00aef0;
  background: #f8faff;
}

.upload-area.drag-active {
  border-color: #00aef0;
  background: #eff6ff;
  transform: scale(1.02);
}

.upload-area.has-file {
  border-color: #10b981;
  background: #f0fdf4;
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;

}

.upload-icon {
  color: #6b7280;
}

.upload-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.upload-description {
  color: #6b7280;
  margin: 0;
}

.browse-text {
  color: #00aef0;
  font-weight: 500;
  text-decoration: underline;
}

.browse-text:hover {
  color: #9ca3af;
}

.upload-info {
  font-size: 0.875rem;
  color: #9ca3af;
  margin: 0;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  max-width: 300px;
}

.preview-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.25rem 0;
  word-break: break-all;
}

.file-size {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.5rem 1rem;
  margin-top: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  width: 100%;
}

.remove-btn:hover {
  background: #dc2626;
  color: white;
}

@media (max-width: 640px) {
  .upload-area {
    padding: 1.5rem;
    min-height: 150px;
  }

  .file-preview {
    flex-direction: column;
    text-align: center;
  }

  .file-details {
    text-align: center;
  }

  .preview-image {
    width: 100px;
    height: 100px;
  }
}


.thankyou-icon {
  margin-bottom: 30px;
  align-self: center;
}

.thankyou-icon svg {
  max-width: 100%; /* Ensure SVG scales down on smaller screens */
  height: auto; /* Maintain aspect ratio */
  align-content: center;
}

.thankyou-title {
  font-size: 3rem;
  font-weight: 700;
  color: #00aef0;
  margin-bottom: 20px;
    align-content: center;
    text-align: center;
}

.thankyou-message {
  font-size: 2rem;
  color:#6b7280;;
  margin-bottom: 32px;
  max-width: 800px; /* Limit width for better readability */
  line-height: 1.5;
    align-content: center;
    text-align: center;
}
.thankyou-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  background: #fff;
  color: #00aef0;
  border-radius: 50px;
  border: 2px solid #00aef0;
  font-size: 1.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: background 0.2s;
  text-align: center;
  padding: 0.5rem 2rem;
  box-sizing: border-box;
  cursor: pointer;
  box-shadow: 0 2px 8px 0 rgba(0, 174, 240, 0.10);
}
.thankyou-btn:hover {
  background: #00aef0;
  color: #fff;
   border: 2px solid #00aef0;}

.phone-input-container {
  display: flex;
  align-items: center;
  padding: 0;
}

.phone-input-container .vue-country-select {
  border: none;
  height: 3rem;
  max-width: 80px; 
  min-width: 60px;
  background: transparent;
  box-shadow: none;
  outline: none;
}

.phone-input-container .vue-country-select:hover,
.phone-input-container .vue-country-select:focus {
  background: transparent;
  box-shadow: none;
}
</style>