
<template>
    <AntiCheatingPolicy :show="showAntiCheat" :toggleModal="toggleAntiCheat" />

    <div class="cheating-container">
        <div class="w-full flex justify-between p-3 bg-white border-b items-center rounded mb-5 shadow-card">
            <button class="w-[60px] h-[50px] rounded-md text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 block" @click="goBackFunction()">
                <font-awesome-icon :icon="['fas', 'angle-left']" />
            </button>
            <div class="flex items-center justify-end gap-5">
                <p>{{ $t("Based on the analyse you did below, do you consider this candidate a cheater?") }}</p>
                <div class="flex items-center justify-center gap-2">
                    <button @click="deleteCheater()" class="w-[80px] h-[50px] rounded-md border-[1.5px] border-gray-200 text-gray-700 hover:bg-gray-50 block">{{ $t("No") }}</button>
                    <button @click="validateCheater()" class="nextStep ml-auto">{{ $t("Yes") }}</button>
                </div>
            </div>
        </div>
        <div v-if="isLoading" class="loading">
            <LoaderComponentBlue />
        </div>
        <div v-else class="flex flex-col w-full gap-5">
            <div class="flex lg:flex-row gap-5 w-[100%]">
                <div class="w-full lg:w-[50%] p-6 rounded-md shadow-card bg-white">
                    <span class="recentapp flex items-center justify-between w-full">
                        {{ $t("Candidate Card") }}
                        <font-awesome-icon @click="showAntiCheat = true" class="font-light text-xs w-3 h-3 p-1 ml-3 rounded-full border border-slate-700" :icon="['fas', 'question']"
                    /></span>
                    <div class="flex flex-col justify-start items-start gap-3 py-6 px-1">
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-light">{{ $t("Device used") }}</h2>
                            <span class="font-bold text-sm">{{ $t("Desktop") }}</span>
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-light">{{ $t("Location") }}</h2>
                            <span class="font-bold text-sm">{{ candidateInfo?.Location }}</span>
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Mouse always in assessment window?") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': candidate?.mouseExited, 'bg-green-800': !candidate?.mouseExited }"
                            >
                                {{ !candidate?.mouseExited ? $t("Yes") : $t("No") }}</span
                            >
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Full-screen mode always active?") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': candidate?.fullScreenExited, 'bg-green-800': !candidate?.fullScreenExited }"
                            >
                                {{ !candidate?.fullScreenExited ? $t("Yes") : $t("No") }}</span
                            >
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Stayed within assessment window?") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': candidate?.exitedTab, 'bg-green-800': !candidate?.exitedTab }"
                            >
                                {{ !candidate?.exitedTab ? $t("Yes") : $t("No") }}</span
                            >
                        </div>
                        <div v-if="candidate?.exitedTab" class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Number of times window has changed:") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': candidate?.exitCount > 0, 'bg-green-800': candidate?.exitCount === 0 }"
                            >
                                {{ candidate?.exitCount }}</span
                            >
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-[50%] p-6 rounded-md shadow-card bg-white">
                    <span class="recentapp flex items-center justify-between w-full">{{ $t("Personal Informations") }}</span>
                    <div class="flex items-start gap-5 py-[2rem] px-1">
                        <img loading="lazy" class="w-[30%] h-[12rem] rounded border border-[#D9D9D9]" decoding="async" :src="'data:image/png;base64,' + this.cheater.avatar" alt="" />

                        <div class="flex w-[70%] flex-col gap-5 items-start">
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-light">{{ $t("First Name") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.first_name }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-light">{{ $t("Last Name") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.second_name }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-thin">{{ $t("Email") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.email }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-thin">{{ $t("Phone Number") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.photo }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full p-6 rounded-md shadow-card bg-white">
                <span class="recentapp">{{ $t("Captured Photos") }}</span>
                <div v-if="captured_photos" class="captured-photos">
                    <div v-for="photo in captured_photos" :key="photo._id">
                        <img loading="lazy" decoding="async" :src="photo.image" alt="" />
                        <span class="date">{{ formatPhotoDate(photo.Date) }}</span>
                    </div>
                </div>
                <div v-if="!captured_photos || captured_photos.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                    <div class="flex justify-center items-center">
                        <img src="../../../assets/No-Picture-Captured.svg" alt="No captured pic" class="w-[40%] h-auto" />
                    </div>
                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">
                        {{ $t("No Picture Captured") }}
                    </h2>
                    <div class="flex flex-col justify-center items-center">
                        <span class="text-center w-[50%]">{{ $t("noPictureCapturedMessage") }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LoaderComponentBlue from "@/components/LoaderComponentBlue";
import AntiCheatingPolicy from "@/components/AntiCheatingPolicy.vue";
import { BASE_URL } from "@/constants";
//import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
import { useStore } from "@/store/index";
import axios from "axios";

export default {
    name: "CheatingCard",
    components: {
        LoaderComponentBlue,
        AntiCheatingPolicy,
        //   ButtonComponent
    },
    data() {
        return {
            currentIndex: 1,
            cheater: {},
            candidate: {},
            captured_photos: [],
            candidateEmail: "",
            project_id: "",
            isLoading: true,
            showAntiCheat: false,
            candidateInfo: this.Store.candidateInfoAct,
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        async getCandidateInv() {
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/candidates/candidateInfo`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.cheater.email,
                    projectId: this.cheater.project_id,
                },
                withCredentials: true,
            };

            axios
                .request(config)
                .then((response) => {
                    this.candidateInfo = response.data.candidateInfo;
                    this.candidate = response.data.candidateScore;
                    this.isLoading = false;
                })
                .catch((error) => {
                    console.log(error);
                    this.isLoading = false;
                });
        },
        toggleAntiCheat() {
            this.showAntiCheat = !this.showAntiCheat;
        },
        formatPhotoDate(dateString) {
            const date = new Date(dateString);
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const seconds = date.getSeconds();

            const formattedTime = `${hours % 12 || 12}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
            const period = hours < 12 ? "AM" : "PM";

            return `${formattedTime} ${period}`;
        },
        deleteCheater() {},
        getCapturedPhotos() {
            console.log("getCapturedPhotos called");
            console.log("Fetching photos for candidate ID:", this.$route.params.id);
            console.log("Full route params:", this.$route.params);

            axios
                .get(`${BASE_URL}/anticheat/photos/${this.$route.params.id}`, { withCredentials: true })
                .then((response) => {
                    console.log("Photos response:", response.data);
                    console.log("Number of photos received:", response.data.length);
                    this.captured_photos = response.data;

                    if (response.data.length === 0) {
                        console.log("No photos found for this candidate");
                    }
                })
                .catch((error) => {
                    console.error("Error fetching photos:", error);
                    console.error("Error details:", error.response);
                });
        },
        validateCheater() {
            axios
                .put(`${BASE_URL}/anticheat/changeToCheater/${this.$route.params.id}`, { withCredentials: true })
                .then(() => {
                    console.log("Changed to cheater succefully");
                })
                .then(() => this.deletePhotos())
                .then(() => this.goBackFunction())
                .catch((error) => {
                    console.log("fail change to cheater -------", error);
                    // this.isLoading = false;
                });
        },
        deletePhotos() {
            axios
                .delete(`${BASE_URL}/anticheat/delete-photos/${this.$route.params.id}`, { withCredentials: true })
                .then(() => {
                    console.log("deleted photos succefully");
                })

                .catch((error) => {
                    console.log("fail deleting photos", error);
                    // this.isLoading = false;
                });
        },
        getCheaterId() {
            axios
                .get(`${BASE_URL}/anticheat/cheater/${this.$route.params.id}`, { withCredentials: true })
                .then((response) => {
                    this.cheater = response.data;
                })
                .then(() => this.getCandidateInv())
                .catch((error) => {
                    console.log(error);
                });
        },
        goBackFunction() {
            this.$router.go(-1);
        },
    },
    mounted() {
        this.getCheaterId();
        this.getCapturedPhotos();
    },
};
</script>
<style scoped>
.nextStep {
    width: 80px;
    height: 50px;
    color: white;
    font-weight: 500;
    background: #2196f3;
    border-radius: 6px;

    &:hover {
        opacity: 0.85;
    }
}

.cheating-container {
    height: 100%;
    width: 100%;
    display: flex;
    padding-top: 40px;
    margin: 0;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.captured-photos {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 1rem;
}

.captured-photos > div {
    width: 25%;
    /* Four columns, each taking 25% of the width */
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.captured-photos img {
    width: 100%;
    height: 170px;
}

.recentapp {
    color: #2196f3;
    font-family: DM Sans;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
}

.loading {
    width: 100%;
    height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
