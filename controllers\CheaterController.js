const express = require('express');
const validator = require('validator');
const Candidate = require('../models/Cheater');
const CandidateModel = require('../models/Candidate');
const CapturedPhotos = require('../models/CapturedPhotos');
const jwt = require("jsonwebtoken");
const Photo_Files = require("../models/photos.files");
const Photo_chunks = require("../models/photos.chunks");



// Get all cheaters
const getAllCheaters = async (req, res) => {
    try {
        const projectId = req.params.id;

        const cheaterCandidates = await Candidate.find({ status: 'cheater', project_id: projectId })
            .populate('candidate_id');

        if (!cheaterCandidates) return res.status(204).json({ message: "No Cheater found." });
        res.json(cheaterCandidates);
    } catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }


};
const getAll = async (req, res) => {
    try {

        const cheaterCandidates = await Candidate.find({}).populate('candidate_id');
        if (!cheaterCandidates) return res.status(204).json({ message: "No Cheater found." });
        res.json(cheaterCandidates);
    } catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }


};

// Get all potential cheaters
const getAllPotentialCheaters = async (req, res) => {
    try {
        const projectId = req.params.id;

        const cheaterCandidates = await Candidate.find({ status: 'potential-cheater', project_id: projectId })
            .populate('candidate_id');

        if (!cheaterCandidates) return res.status(204).json({ message: "No Cheater found." });
        res.json(cheaterCandidates);
    } catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }


};

const getCheater = async (req, res) => {
    try {
        const candidate = await Candidate.findById(req.params.id).populate('candidate_id');

        if (!candidate) {
            console.log('Candidate not found');
            return null;
        }

        let Avatar = "";
        if (candidate.candidate_id.Avatar && !candidate.candidate_id.Avatar.toLowerCase().includes(".jpg")) {
            Avatar = await Photo_Files.findOne({
                _id: candidate.candidate_id.Avatar,
            });
        }

        if (Avatar) {
            candidate.candidate_id.Avatar = "";

            let imgChunks = await Photo_chunks.find({
                files_id: Avatar._id,
            });

            imgChunks.map((chunk) => {
                candidate.candidate_id.Avatar += chunk.data.toString("base64");
            });

            console.log({ candidateImage: candidate.candidate_id.Avatar });
        }


        res.json({ project_id: candidate.project_id, cheater_id: candidate._id, avatar: candidate.candidate_id.Avatar, first_name: candidate.candidate_id.First_name, second_name: candidate.candidate_id.Last_name, candidateId: candidate.candidate_id._id, status: candidate.status, email: candidate.candidate_id.Email, phone: candidate.candidate_id.Phone });
    } catch (error) {
        console.error('Error fetching candidate:', error);
        throw error; // You may want to handle or log the error accordingly
    }

}


const getCheaterByCandidateAndProject = async (req, res) => {
    try {
        const { email, project_id } = req.params;

        // Find candidate by email
        const candidate = await CandidateModel.findOne({ Email: email.trim() });
        if (!candidate) {
            return res.status(404).json({ message: "Candidate not found." });
        }

        // Use candidate_id from the candidate record to find cheater
        const cheaterCandidate = await Candidate.findOne({
            candidate_id: candidate._id,
            project_id: project_id
        }).populate('candidate_id');
        if (!cheaterCandidate) {
            return res.json({ cheater: null, message: "Cheater not found for the given candidate and project." });
        }

        res.json({ cheater: cheaterCandidate });
    } catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
};


// Get all captured photos by candidate
const getAllPhotosbyCandidate = async (req, res) => {
    try {
        console.log("Searching for photos with idCandidate:", req.params.id);
        const capturedPhotos = await CapturedPhotos.find({ idCandidate: req.params.id });
        console.log("Found photos:", capturedPhotos.length);
        res.json(capturedPhotos);


    } catch (error) {
        console.error("Error fetching photos:", error);
        res.status(500).json({ error: 'Internal Server Error' });

    }

};


// Create a new Cheater
const createCheater = async (req, res) => {
    console.log(req.cookies);

    let token = req.cookies.candidatecookie;

    console.log({ token });
    jwt.verify(token, process.env.SPECIAL_LINK_KEY, async (err, decoded) => {
        if (err) {
            console.log({ err });
            return res.status(401).json({
                title: "unauthorized",
            });
        }

        try {

            const cheaterFound = await Candidate.findOne({ candidate_id: req.params.id, project_id: decoded.projectId });

            if (!cheaterFound) {
                // Create a new captured photo
                const cheater = new Candidate({
                    candidate_id: req.params.id,
                    project_id: decoded.projectId
                    // Add other captured photo fields as needed
                });

                const savedCheater = await cheater.save();

                return res.status(201).json(savedCheater);
            }

            res.status(201).json(cheaterFound);

        } catch (err) {

            res.status(500).json({ error: 'Internal Server Error' });
        }
    });
};
// Create a new CapturedPhoto
const createCapturedPhoto = async (req, res) => {
    const { photos } = req.body; // Expect an array of photos with dates

    try {
        // Check if the candidate exists
        const candidateExists = await Candidate.findById(req.params.idCandidate);
        if (!candidateExists) {
            return res.status(404).json({ error: 'Cheater Candidate not found' });
        }

        console.log("Storing photos for idCandidate:", candidateExists._id);
        console.log("Number of photos to store:", photos.length);

        // Create multiple captured photos
        const capturedPhotos = photos.map(photo => ({
            idCandidate: candidateExists._id,
            image: photo.image,
            Date: photo.date, // Store the date for each image
        }));

        // Save all captured photos
        const savedCapturedPhotos = await CapturedPhotos.insertMany(capturedPhotos);

        console.log("Successfully stored photos:", savedCapturedPhotos.length);

        res.status(201).json(savedCapturedPhotos);

    } catch (err) {
        console.log("Successfully stored photos:", savedCapturedPhotos.length);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};


// Update the status of a candidate to cheater
const changeToCheater = async (req, res) => {
    const { id } = req.params;

    try {
        // Find the candidate by ID
        const candidate = await Candidate.findById(id);

        // Update the status to "cheater"
        candidate.status = 'cheater';

        // Save the updated candidate to the database
        const updatedCandidate = await candidate.save();
        res.json(updatedCandidate);


    }
    catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
};



// Update the status of a candidate to non cheater
const deleteCheater = async (req, res) => {
    const { id } = req.params;

    try {
        // Find the candidate by ID
        const candidate = await Candidate.findByIdAndDelete(id);

        res.json(candidate);
    }
    catch (error) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
};

const deletePhotos = async (req, res) => {
    const { id } = req.params;

    try {
        // Find the candidate by ID
        const photos = await CapturedPhotos.deleteMany({ idCandidate: id });

        res.json(photos);
    }
    catch (error) {
        res.status(500).json({ error: error });
    }
};

module.exports = {
    getAllCheaters,
    getCheater,
    getAllPotentialCheaters,
    createCapturedPhoto,
    getAllPhotosbyCandidate,
    changeToCheater,
    deleteCheater,
    createCheater,
    getAll,
    deletePhotos,
    getCheaterByCandidateAndProject
};
